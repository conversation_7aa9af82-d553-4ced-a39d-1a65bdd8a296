# 兼容API密钥快速开始指南

## 5分钟快速上手

### 步骤1: 准备工作

确保您已经：
- ✅ 部署了应用并启动服务
- ✅ 获得了有效的JWT Token
- ✅ 拥有各AI提供商的原始API密钥

### 步骤2: 添加普通API密钥

```bash
# 添加OpenAI密钥
curl -X POST "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "OPENAI",
    "keyName": "OpenAI主密钥",
    "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "priority": 1
  }'

# 添加Claude密钥
curl -X POST "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "ANTHROPIC",
    "keyName": "Claude主密钥",
    "apiKey": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "priority": 1
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "id": 1,
    "provider": "OPENAI",
    "keyName": "OpenAI主密钥",
    "maskedApiKey": "sk-****...****abcd",
    "isActive": true,
    "priority": 1
  }
}
```

### 步骤3: 创建兼容API密钥

```bash
curl -X POST "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "keyName": "我的第一个兼容密钥",
    "description": "包含OpenAI和Claude的测试密钥",
    "apiKeyMappings": [
      {
        "userApiKeyId": 1,
        "weight": 30
      },
      {
        "userApiKeyId": 2,
        "weight": 20
      }
    ]
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "compatibleKey": "sk-comp-1234567890123456789-a1b2c3",
    "keyName": "我的第一个兼容密钥",
    "isActive": true,
    "mappedApiKeys": [
      {
        "apiKeyId": 1,
        "keyName": "OpenAI主密钥",
        "provider": "OPENAI",
        "weight": 30,
        "isHealthy": true
      },
      {
        "apiKeyId": 2,
        "keyName": "Claude主密钥",
        "provider": "ANTHROPIC",
        "weight": 20,
        "isHealthy": true
      }
    ]
  }
}
```

### 步骤4: 使用兼容API密钥

现在您可以使用生成的兼容密钥 `sk-comp-1234567890123456789-a1b2c3` 来调用AI服务：

```bash
# 使用GPT-4（会自动选择OpenAI密钥）
curl -X POST "http://localhost:8080/api/openai/v1/chat/completions" \
  -H "Authorization: Bearer sk-comp-1234567890123456789-a1b2c3" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 100
  }'

# 使用Claude（会自动选择Anthropic密钥）
curl -X POST "http://localhost:8080/api/openai/v1/chat/completions" \
  -H "Authorization: Bearer sk-comp-1234567890123456789-a1b2c3" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 100
  }'
```

## 常用操作

### 查看兼容密钥列表

```bash
curl -X GET "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 查看兼容密钥详情

```bash
curl -X GET "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 禁用兼容密钥

```bash
curl -X POST "http://localhost:8080/api/ai/compatible-keys/1/toggle?isActive=false" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 添加新的API密钥映射

```bash
curl -X POST "http://localhost:8080/api/ai/compatible-keys/1/mappings?userApiKeyId=3&weight=15" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 最佳实践

### 1. 权重配置建议

```json
{
  "apiKeyMappings": [
    {
      "userApiKeyId": 1,
      "weight": 40,
      "comment": "主要密钥 - OpenAI GPT-4"
    },
    {
      "userApiKeyId": 2,
      "weight": 30,
      "comment": "备用密钥 - Claude 3"
    },
    {
      "userApiKeyId": 3,
      "weight": 20,
      "comment": "备用密钥 - Gemini Pro"
    },
    {
      "userApiKeyId": 4,
      "weight": 10,
      "comment": "测试密钥 - GPT-3.5"
    }
  ]
}
```

### 2. 环境分离

```bash
# 开发环境兼容密钥
{
  "keyName": "开发环境密钥",
  "description": "用于开发和测试",
  "apiKeyMappings": [
    {"userApiKeyId": 5, "weight": 50}  // 测试密钥
  ]
}

# 生产环境兼容密钥
{
  "keyName": "生产环境密钥",
  "description": "生产环境高可用配置",
  "apiKeyMappings": [
    {"userApiKeyId": 1, "weight": 40},  // 主要密钥
    {"userApiKeyId": 2, "weight": 30},  // 备用密钥1
    {"userApiKeyId": 3, "weight": 20},  // 备用密钥2
    {"userApiKeyId": 4, "weight": 10}   // 应急密钥
  ],
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

### 3. 监控和维护

```bash
# 定期检查密钥健康状态
curl -X GET "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | \
  jq '.data.healthStats'

# 查看使用统计
curl -X GET "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | \
  jq '.data[] | {keyName, usageCount, lastUsedAt}'
```

## 故障排除

### 问题1: 兼容密钥验证失败

**错误信息**: `Invalid API key format`

**解决方案**:
```bash
# 检查密钥格式
echo "sk-comp-1234567890123456789-a1b2c3" | grep -E "^sk-comp-[0-9]+-[a-zA-Z0-9]+$"

# 验证密钥状态
curl -X POST "http://localhost:8080/api/ai/test/parse-key" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "sk-comp-1234567890123456789-a1b2c3"}'
```

### 问题2: 没有可用的API密钥

**错误信息**: `No available API key for model: gpt-4`

**解决方案**:
```bash
# 检查映射的密钥状态
curl -X GET "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | \
  jq '.data.mappedApiKeys[] | {keyName, provider, isActive, isHealthy}'

# 检查普通密钥状态
curl -X GET "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" | \
  jq '.data[] | {id, keyName, provider, isActive}'
```

### 问题3: 权重配置不生效

**解决方案**:
```bash
# 更新权重配置
curl -X PUT "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "keyName": "更新后的密钥",
    "apiKeyMappings": [
      {"userApiKeyId": 1, "weight": 50},
      {"userApiKeyId": 2, "weight": 30}
    ]
  }'
```

## 下一步

- 📖 阅读 [完整使用指南](./兼容API密钥使用指南.md)
- 📋 查看 [API接口文档](./兼容API密钥接口文档.md)
- 🔧 了解 [系统重构说明](./兼容API密钥系统重构说明.md)

## 技术支持

如果遇到问题，请：
1. 检查系统日志获取详细错误信息
2. 使用测试接口验证密钥状态
3. 参考故障排除指南
4. 联系技术支持团队
