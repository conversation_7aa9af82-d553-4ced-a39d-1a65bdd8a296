# 兼容API密钥系统重构说明

## 概述

本次重构将原有的自动生成兼容API密钥功能改为按需生成，实现了一个兼容密钥对应多个普通API密钥的架构，并使用Spring Security的加密器替换了原有的加密方案。

## 主要变更

### 1. 架构变更

**原架构：**
- 添加普通API密钥时自动生成兼容密钥
- 一对一关系：一个普通密钥对应一个兼容密钥
- 格式：`sk-{userId}_{keyId}_{hash}`

**新架构：**
- 按需生成兼容密钥
- 一对多关系：一个兼容密钥对应多个普通密钥
- 格式：`sk-comp-{snowflakeId}-{hash}`

### 2. 数据库设计

#### 新增表结构

**compatible_api_keys 表：**
```sql
- id: 主键ID
- user_id: 用户ID
- snowflake_id: 雪花算法生成的唯一ID
- key_name: 兼容密钥名称
- compatible_key: 兼容格式的API密钥
- security_hash: 安全哈希值
- is_active: 是否激活
- description: 描述信息
- usage_count: 使用次数统计
- last_used_at: 最后使用时间
- created_at: 创建时间
- updated_at: 更新时间
- expires_at: 过期时间
```

**compatible_key_mappings 表：**
```sql
- id: 主键ID
- compatible_key_id: 兼容API密钥ID
- user_api_key_id: 普通API密钥ID
- weight: 权重（用于负载均衡）
- is_active: 是否激活
- created_at: 创建时间
- updated_at: 更新时间
```

### 3. 核心组件

#### 新增组件

1. **SnowflakeIdGenerator**: 雪花算法ID生成器
2. **SpringEncryptionUtil**: 使用Spring Security的加密工具
3. **CompatibleApiKey**: 兼容密钥实体
4. **CompatibleKeyMapping**: 密钥映射关系实体
5. **CompatibleKeyService**: 兼容密钥管理服务
6. **CompatibleKeyController**: 兼容密钥管理控制器

#### 修改组件

1. **AiConfigController**: 移除自动生成兼容密钥逻辑
2. **OpenAiCompatibleServiceImpl**: 使用新的兼容密钥验证逻辑
3. **ApiKeyEncryptionUtil**: 改为向后兼容的包装器

### 4. API接口

#### 新增接口

**兼容密钥管理：**
- `POST /api/ai/compatible-keys` - 创建兼容密钥
- `GET /api/ai/compatible-keys` - 获取兼容密钥列表
- `GET /api/ai/compatible-keys/{keyId}` - 获取兼容密钥详情
- `PUT /api/ai/compatible-keys/{keyId}` - 更新兼容密钥
- `DELETE /api/ai/compatible-keys/{keyId}` - 删除兼容密钥
- `POST /api/ai/compatible-keys/{keyId}/toggle` - 启用/禁用兼容密钥
- `POST /api/ai/compatible-keys/{keyId}/mappings` - 添加API密钥映射
- `DELETE /api/ai/compatible-keys/{keyId}/mappings/{mappingId}` - 移除API密钥映射

#### 修改接口

**普通API密钥管理：**
- `POST /api/ai/config/api-keys` - 不再自动生成兼容密钥

### 5. 使用流程

#### 创建兼容密钥

1. 用户先添加普通API密钥
2. 调用创建兼容密钥接口，选择要关联的普通密钥
3. 系统生成兼容密钥并建立映射关系
4. 用户可以使用兼容密钥进行API调用

#### 兼容密钥验证流程

1. 解析兼容密钥格式
2. 验证雪花ID和哈希值
3. 检查兼容密钥状态（激活、过期）
4. 根据请求模型选择合适的普通密钥
5. 使用选中的普通密钥进行API调用

### 6. 负载均衡策略

- 根据权重选择API密钥
- 优先选择健康状态良好的密钥
- 支持按提供商类型筛选密钥

### 7. 安全增强

- 使用Spring Security的TextEncryptor进行加密
- 雪花算法确保ID的全局唯一性
- 安全哈希验证防止密钥伪造
- 支持密钥过期时间设置

### 8. 向后兼容

- 保留原有的ApiKeyEncryptionUtil作为包装器
- 废弃的方法会抛出UnsupportedOperationException
- 提供清晰的迁移指导

## 配置说明

### application.yml 配置

```yaml
app:
  # Spring加密器配置
  encryption:
    password: "your-encryption-password"
    salt: "your-encryption-salt"
  
  # 雪花算法配置
  snowflake:
    machine-id: 1
    datacenter-id: 1
```

## 迁移指南

### 对于现有用户

1. 现有的普通API密钥继续正常工作
2. 如需使用兼容密钥功能，需要重新创建兼容密钥
3. 原有的自动生成兼容密钥功能已移除

### 对于开发者

1. 使用`CompatibleKeyService`替代原有的兼容密钥方法
2. 使用`SpringEncryptionUtil`替代`ApiKeyEncryptionUtil`
3. 更新API调用逻辑以适应新的接口

## 优势

1. **灵活性**: 一个兼容密钥可以管理多个提供商的密钥
2. **安全性**: 使用Spring Security标准加密，支持密钥过期
3. **性能**: 雪花算法确保高性能ID生成
4. **可扩展性**: 支持权重配置和负载均衡
5. **可维护性**: 清晰的架构分层和职责分离

## 注意事项

1. 需要执行数据库迁移脚本创建新表
2. 原有的兼容密钥格式不再支持
3. 建议在生产环境部署前进行充分测试
4. 配置合适的雪花算法机器ID和数据中心ID
