# 兼容API密钥使用指南

## 概述

兼容API密钥系统允许您创建一个统一的密钥来管理多个AI提供商的API密钥，支持自动负载均衡和故障转移。

## 快速开始

### 1. 添加普通API密钥

首先，您需要添加各个AI提供商的原始API密钥：

```bash
# 添加OpenAI密钥
curl -X POST "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "OPENAI",
    "keyName": "OpenAI主密钥",
    "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "priority": 1
  }'

# 添加Anthropic密钥
curl -X POST "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "ANTHROPIC",
    "keyName": "Claude主密钥",
    "apiKey": "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "priority": 1
  }'

# 添加Google密钥
curl -X POST "http://localhost:8080/api/ai/config/api-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "GOOGLE",
    "keyName": "Gemini主密钥",
    "apiKey": "AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "priority": 1
  }'
```

### 2. 创建兼容API密钥

使用添加的普通密钥创建兼容密钥：

```bash
curl -X POST "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "keyName": "生产环境主密钥",
    "description": "包含所有提供商的生产环境密钥",
    "apiKeyMappings": [
      {
        "userApiKeyId": 1,
        "weight": 30
      },
      {
        "userApiKeyId": 2,
        "weight": 20
      },
      {
        "userApiKeyId": 3,
        "weight": 10
      }
    ],
    "expiresAt": "2024-12-31T23:59:59Z"
  }'
```

**响应示例：**
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "snowflakeId": 1234567890123456789,
    "keyName": "生产环境主密钥",
    "compatibleKey": "sk-comp-1234567890123456789-a1b2c3",
    "isActive": true,
    "description": "包含所有提供商的生产环境密钥",
    "usageCount": 0,
    "createdAt": "2024-01-15T10:30:00Z",
    "expiresAt": "2024-12-31T23:59:59Z",
    "mappedApiKeys": [
      {
        "apiKeyId": 1,
        "keyName": "OpenAI主密钥",
        "provider": "OPENAI",
        "maskedApiKey": "sk-****...****abcd",
        "weight": 30,
        "isActive": true,
        "isHealthy": true
      }
    ],
    "healthStats": {
      "totalKeys": 3,
      "healthyKeys": 3,
      "unhealthyKeys": 0,
      "totalWeight": 60,
      "availableWeight": 60
    }
  }
}
```

### 3. 使用兼容API密钥

现在您可以使用生成的兼容密钥 `sk-comp-1234567890123456789-a1b2c3` 来调用AI服务：

```bash
# 聊天对话
curl -X POST "http://localhost:8080/api/openai/v1/chat/completions" \
  -H "Authorization: Bearer sk-comp-1234567890123456789-a1b2c3" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "max_tokens": 100
  }'

# 图片生成
curl -X POST "http://localhost:8080/api/openai/v1/images/generations" \
  -H "Authorization: Bearer sk-comp-1234567890123456789-a1b2c3" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "dall-e-3",
    "prompt": "A beautiful sunset over the ocean",
    "n": 1,
    "size": "1024x1024"
  }'
```

## 管理功能

### 查看兼容密钥列表

```bash
curl -X GET "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 查看兼容密钥详情

```bash
curl -X GET "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 更新兼容密钥

```bash
curl -X PUT "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "keyName": "更新后的密钥名称",
    "description": "更新后的描述",
    "apiKeyMappings": [
      {
        "userApiKeyId": 1,
        "weight": 40
      },
      {
        "userApiKeyId": 2,
        "weight": 30
      }
    ]
  }'
```

### 启用/禁用兼容密钥

```bash
# 禁用
curl -X POST "http://localhost:8080/api/ai/compatible-keys/1/toggle?isActive=false" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 启用
curl -X POST "http://localhost:8080/api/ai/compatible-keys/1/toggle?isActive=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 添加API密钥映射

```bash
curl -X POST "http://localhost:8080/api/ai/compatible-keys/1/mappings?userApiKeyId=4&weight=15" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 移除API密钥映射

```bash
curl -X DELETE "http://localhost:8080/api/ai/compatible-keys/1/mappings/2" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 删除兼容密钥

```bash
curl -X DELETE "http://localhost:8080/api/ai/compatible-keys/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 负载均衡策略

### 权重配置

- **权重值**: 数字越大，被选中的概率越高
- **建议配置**:
  - 主要密钥: 30-50
  - 备用密钥: 10-20
  - 测试密钥: 5-10

### 自动选择逻辑

1. **按模型筛选**: 根据请求的模型类型筛选对应提供商的密钥
2. **健康状态优先**: 优先选择健康状态良好的密钥
3. **权重排序**: 在健康密钥中按权重选择
4. **故障转移**: 如果首选密钥不可用，自动切换到备用密钥

## 支持的模型

### OpenAI
- GPT系列: `gpt-4`, `gpt-4-turbo`, `gpt-3.5-turbo`
- 图片生成: `dall-e-3`, `dall-e-2`
- 其他: `text-davinci-003`, `o1-preview`

### Anthropic
- Claude系列: `claude-3-sonnet`, `claude-3-opus`, `claude-3-haiku`
- 新版本: `claude-4-*`

### Google
- Gemini系列: `gemini-pro`, `gemini-1.5-pro`, `gemini-2.0-flash-exp`
- 图片生成: `imagen-*`

## 最佳实践

### 1. 密钥配置

```json
{
  "keyName": "生产环境密钥",
  "description": "生产环境使用的主要密钥组合",
  "apiKeyMappings": [
    {
      "userApiKeyId": 1,
      "weight": 40,
      "comment": "OpenAI主密钥 - 高权重"
    },
    {
      "userApiKeyId": 2,
      "weight": 30,
      "comment": "Claude备用密钥 - 中权重"
    },
    {
      "userApiKeyId": 3,
      "weight": 20,
      "comment": "Gemini备用密钥 - 低权重"
    }
  ],
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

### 2. 监控和维护

- 定期检查密钥健康状态
- 监控使用统计和错误率
- 及时更新过期密钥
- 根据使用情况调整权重

### 3. 安全建议

- 设置合理的过期时间
- 定期轮换API密钥
- 监控异常使用情况
- 使用HTTPS进行所有API调用

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `401`: 兼容密钥无效或已过期
- `404`: 兼容密钥不存在
- `500`: 服务器内部错误

### 错误响应示例

```json
{
  "code": 401,
  "message": "兼容密钥已过期",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 故障排除

### 1. 兼容密钥验证失败

- 检查密钥格式是否正确
- 确认密钥未过期
- 验证密钥是否处于激活状态

### 2. 没有可用的API密钥

- 检查映射的普通密钥是否激活
- 确认普通密钥的健康状态
- 验证模型与提供商的匹配关系

### 3. 负载均衡异常

- 检查权重配置是否合理
- 确认多个密钥的健康状态
- 查看负载均衡统计信息

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看系统日志获取详细错误信息
2. 使用测试接口验证密钥状态
3. 联系技术支持团队获取帮助

---

**注意**: 请妥善保管您的兼容API密钥，避免泄露给未授权人员。
