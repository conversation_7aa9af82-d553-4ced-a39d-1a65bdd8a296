# 兼容API密钥接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/ai`
- **认证方式**: Bear<PERSON> (JWT)
- **Content-Type**: `application/json`

## 接口概览

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/compatible-keys` | 创建兼容API密钥 |
| GET | `/compatible-keys` | 获取兼容API密钥列表 |
| GET | `/compatible-keys/{keyId}` | 获取兼容API密钥详情 |
| PUT | `/compatible-keys/{keyId}` | 更新兼容API密钥 |
| DELETE | `/compatible-keys/{keyId}` | 删除兼容API密钥 |
| POST | `/compatible-keys/{keyId}/toggle` | 启用/禁用兼容API密钥 |
| POST | `/compatible-keys/{keyId}/mappings` | 添加API密钥映射 |
| DELETE | `/compatible-keys/{keyId}/mappings/{mappingId}` | 移除API密钥映射 |

## 详细接口说明

### 1. 创建兼容API密钥

**接口**: `POST /compatible-keys`

**请求体**:
```json
{
  "keyName": "生产环境主密钥",
  "description": "包含所有提供商的生产环境密钥",
  "apiKeyMappings": [
    {
      "userApiKeyId": 1,
      "weight": 30
    },
    {
      "userApiKeyId": 2,
      "weight": 20
    }
  ],
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "snowflakeId": 1234567890123456789,
    "keyName": "生产环境主密钥",
    "compatibleKey": "sk-comp-1234567890123456789-a1b2c3",
    "isActive": true,
    "description": "包含所有提供商的生产环境密钥",
    "usageCount": 0,
    "lastUsedAt": null,
    "createdAt": "2024-01-15T10:30:00Z",
    "expiresAt": "2024-12-31T23:59:59Z",
    "mappedApiKeys": [
      {
        "mappingId": 1,
        "apiKeyId": 1,
        "keyName": "OpenAI主密钥",
        "provider": "OPENAI",
        "maskedApiKey": "sk-****...****abcd",
        "weight": 30,
        "isActive": true,
        "isHealthy": true
      }
    ],
    "healthStats": {
      "totalKeys": 2,
      "healthyKeys": 2,
      "unhealthyKeys": 0,
      "totalWeight": 50,
      "availableWeight": 50
    }
  }
}
```

### 2. 获取兼容API密钥列表

**接口**: `GET /compatible-keys`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "keyName": "生产环境主密钥",
      "compatibleKey": "sk-comp-1234567890123456789-a1b2c3",
      "isActive": true,
      "usageCount": 150,
      "lastUsedAt": "2024-01-15T15:30:00Z",
      "createdAt": "2024-01-15T10:30:00Z",
      "healthStats": {
        "totalKeys": 2,
        "healthyKeys": 2,
        "unhealthyKeys": 0
      }
    }
  ]
}
```

### 3. 启用/禁用兼容API密钥

**接口**: `POST /compatible-keys/{keyId}/toggle?isActive=true`

**路径参数**:
- `keyId`: 兼容密钥ID

**查询参数**:
- `isActive`: boolean，是否激活

### 4. 添加API密钥映射

**接口**: `POST /compatible-keys/{keyId}/mappings?userApiKeyId=3&weight=15`

**路径参数**:
- `keyId`: 兼容密钥ID

**查询参数**:
- `userApiKeyId`: 普通API密钥ID
- `weight`: 权重值（可选，默认10）

## 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权访问 | 检查JWT Token |
| 404 | 资源不存在 | 检查密钥ID是否正确 |
| 409 | 资源冲突 | 检查映射关系是否重复 |
| 500 | 服务器错误 | 联系技术支持 |

## 使用示例

### cURL 示例

```bash
# 创建兼容密钥
curl -X POST "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "keyName": "测试密钥",
    "apiKeyMappings": [
      {"userApiKeyId": 1, "weight": 30}
    ]
  }'

# 获取密钥列表
curl -X GET "http://localhost:8080/api/ai/compatible-keys" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 禁用密钥
curl -X POST "http://localhost:8080/api/ai/compatible-keys/1/toggle?isActive=false" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### JavaScript 示例

```javascript
// 创建兼容密钥
async function createCompatibleKey() {
  const response = await fetch('/api/ai/compatible-keys', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      keyName: '测试密钥',
      apiKeyMappings: [
        { userApiKeyId: 1, weight: 30 }
      ]
    })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    console.log('兼容密钥:', result.data.compatibleKey);
  }
}
```

## 数据模型

### 请求模型

```typescript
interface CreateCompatibleKeyRequest {
  keyName: string;               // 必填，密钥名称
  description?: string;          // 可选，描述信息
  apiKeyMappings: ApiKeyMapping[]; // 必填，API密钥映射
  expiresAt?: string;            // 可选，过期时间
}

interface ApiKeyMapping {
  userApiKeyId: number;          // 必填，API密钥ID
  weight?: number;               // 可选，权重，默认10
}
```

### 响应模型

```typescript
interface CompatibleKeyDto {
  id: number;
  snowflakeId: number;
  keyName: string;
  compatibleKey: string;
  isActive: boolean;
  description?: string;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  expiresAt?: string;
  mappedApiKeys: MappedApiKeyDto[];
  healthStats: HealthStats;
}
```

## 注意事项

1. **认证**: 所有接口都需要有效的JWT Token
2. **权限**: 用户只能操作自己的兼容密钥
3. **级联删除**: 删除兼容密钥会删除所有映射关系
4. **权重建议**: 权重值建议在1-100之间
5. **过期检查**: 系统自动检查密钥过期状态

## 版本信息

- **版本**: v1.0.0
- **更新时间**: 2024-01-15
