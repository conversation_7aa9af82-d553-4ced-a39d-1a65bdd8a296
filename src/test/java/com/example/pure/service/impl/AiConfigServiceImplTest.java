package com.example.pure.service.impl;

import com.example.pure.mapper.primary.UserAiConfigMapper;
import com.example.pure.mapper.primary.UserApiKeyMapper;
import com.example.pure.model.entity.UserAiConfig;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.service.openai.ModelAdapterService;
import com.example.pure.service.openai.impl.AiConfigServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * AiConfigServiceImpl 测试类
 * <p>
 * 测试 AI 配置服务的核心功能，特别是 getUserConfig 方法
 * </p>
 */
@ExtendWith(MockitoExtension.class)
class AiConfigServiceImplTest {

    @Mock
    private UserAiConfigMapper userAiConfigMapper;

    @Mock
    private UserApiKeyMapper userApiKeyMapper;

    @Mock
    private LoadBalancerService loadBalancerService;

    @Mock
    private ModelAdapterService modelAdapterService;

    @Mock
    private ApiKeyEncryptionUtil encryptionUtil;

    @InjectMocks
    private AiConfigServiceImpl aiConfigService;

    private Long testUserId;

    @BeforeEach
    void setUp() {
        testUserId = 1L;
    }

    /**
     * 测试获取用户配置 - 用户没有配置时创建默认配置并插入数据库
     */
    @Test
    void testGetUserConfig_WhenUserHasNoConfig_ShouldCreateAndInsertDefaultConfig() {
        // 决策理由：测试当用户没有配置时，系统应该创建默认配置并插入数据库

        // Given: 用户没有现有配置，插入操作成功
        when(userAiConfigMapper.selectByUserId(testUserId)).thenReturn(null);
        when(userAiConfigMapper.insert(any(UserAiConfig.class))).thenReturn(1);

        // When: 获取用户配置
        UserAiConfig result = aiConfigService.getUserConfig(testUserId);

        // Then: 应该返回默认配置
        assertNotNull(result, "应该返回配置对象");
        assertEquals(testUserId, result.getUserId(), "用户ID应该匹配");
        assertEquals("gpt-3.5-turbo", result.getPreferredModel(), "应该使用默认模型");
        assertEquals(new BigDecimal("0.7"), result.getDefaultTemperature(), "应该使用默认温度");
        assertEquals(Integer.valueOf(4096), result.getDefaultMaxTokens(), "应该使用默认最大token数");
        assertEquals(new BigDecimal("1.0"), result.getDefaultTopP(), "应该使用默认top_p值");
        assertTrue(result.getStreamEnabled(), "应该启用流式输出");
        assertEquals(Integer.valueOf(30), result.getTimeoutSeconds(), "应该使用默认超时时间");
        assertEquals("你是一个有用的AI助手", result.getSystemPrompt(), "应该使用默认系统提示词");

        // 验证时间字段为空（由数据库自动设置）
        assertNull(result.getCreatedAt(), "创建时间应该为空，由数据库自动设置");
        assertNull(result.getUpdatedAt(), "更新时间应该为空，由数据库自动设置");

        // 验证 Mapper 被调用
        verify(userAiConfigMapper, times(1)).selectByUserId(testUserId);
        verify(userAiConfigMapper, times(1)).insert(any(UserAiConfig.class));
    }

    /**
     * 测试获取用户配置 - 用户已有配置时直接返回
     */
    @Test
    void testGetUserConfig_WhenUserHasConfig_ShouldReturnExistingConfig() {
        // 决策理由：测试当用户已有配置时，应该直接返回现有配置而不创建新的

        // Given: 用户已有配置
        UserAiConfig existingConfig = new UserAiConfig()
                .setId(1L)
                .setUserId(testUserId)
                .setPreferredModel("gpt-4")
                .setDefaultTemperature(new BigDecimal("0.5"));

        when(userAiConfigMapper.selectByUserId(testUserId)).thenReturn(existingConfig);

        // When: 获取用户配置
        UserAiConfig result = aiConfigService.getUserConfig(testUserId);

        // Then: 应该返回现有配置
        assertNotNull(result, "应该返回配置对象");
        assertEquals(existingConfig.getId(), result.getId(), "应该返回相同的配置ID");
        assertEquals("gpt-4", result.getPreferredModel(), "应该保持用户自定义的模型");
        assertEquals(new BigDecimal("0.5"), result.getDefaultTemperature(), "应该保持用户自定义的温度");

        // 验证 Mapper 只被调用一次查询，没有插入操作
        verify(userAiConfigMapper, times(1)).selectByUserId(testUserId);
        verify(userAiConfigMapper, never()).insert(any(UserAiConfig.class));
    }

    /**
     * 测试插入失败的情况
     */
    @Test
    void testGetUserConfig_WhenInsertFails_ShouldThrowException() {
        // 决策理由：测试插入失败时应该抛出异常

        // Given: 用户没有配置，但插入失败
        when(userAiConfigMapper.selectByUserId(testUserId)).thenReturn(null);
        when(userAiConfigMapper.insert(any(UserAiConfig.class))).thenReturn(0);

        // When & Then: 应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            aiConfigService.getUserConfig(testUserId);
        });

        assertEquals("创建用户AI默认配置失败", exception.getMessage());

        // 验证 Mapper 被调用
        verify(userAiConfigMapper, times(1)).selectByUserId(testUserId);
        verify(userAiConfigMapper, times(1)).insert(any(UserAiConfig.class));
    }
}
