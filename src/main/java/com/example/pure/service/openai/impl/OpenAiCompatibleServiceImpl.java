package com.example.pure.service.openai.impl;

import com.example.pure.mapper.primary.UserApiKeyMapper;
import com.example.pure.model.adapter.ChatCompletionRequest;
import com.example.pure.model.adapter.ChatCompletionResponse;
import com.example.pure.model.adapter.ModelInfo;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.model.dto.response.openai.*;
import com.example.pure.model.entity.UserAiConfig;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.*;
import com.example.pure.util.SpringEncryptionUtil;
import com.example.pure.util.MultimodalContentUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * OpenAI兼容服务实现类
 * <p>
 * 实现OpenAI API格式的服务接口，处理认证、负载均衡和格式转换
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpenAiCompatibleServiceImpl implements OpenAiCompatibleService {

    private final AiConfigService aiConfigService;
    private final LoadBalancerService loadBalancerService;
    private final ModelAdapterService modelAdapterService;
    private final UserApiKeyMapper userApiKeyMapper;
    private final CompatibleKeyMappingMapper compatibleKeyMappingMapper;
    private final SpringEncryptionUtil encryptionUtil;
    private final CompatibleKeyService compatibleKeyService;
    private final SimpleCompatibleKeyService simpleCompatibleKeyService;
    private final ObjectMapper objectMapper;
    private final ReasoningExtractorService reasoningExtractorService;

    /**
     * 处理流式聊天完成请求
     * <p>
     * 这是OpenAI兼容API的流式响应核心方法，使用Server-Sent Events (SSE) 技术
     * 实现实时的AI对话体验。与非流式方法不同，此方法会立即返回SseEmitter，
     * 然后通过异步处理逐步发送响应数据。
     * </p>
     *
     * <h3>核心特性：</h3>
     * <ul>
     *   <li>实时流式响应 - 模拟ChatGPT的逐字输出效果</li>
     *   <li>异步处理 - 避免阻塞主线程，提高并发性能</li>
     *   <li>长连接支持 - 10分钟超时，支持长时间的AI思考过程</li>
     *   <li>推理过程流式传输 - 支持o1模型的思考过程实时展示</li>
     *   <li>错误恢复 - 连接断开时的优雅处理</li>
     *   <li>客户端兼容 - 完全兼容OpenAI的流式API格式</li>
     * </ul>
     *
     * <h3>SSE事件类型：</h3>
     * <ul>
     *   <li><strong>开始事件：</strong>包含响应ID和模型信息</li>
     *   <li><strong>推理事件：</strong>o1模型的思考过程片段</li>
     *   <li><strong>内容事件：</strong>实际的回复内容片段</li>
     *   <li><strong>结束事件：</strong>包含token使用统计</li>
     *   <li><strong>完成标记：</strong>[DONE]标记流式传输结束</li>
     * </ul>
     *
     * <h3>性能优化：</h3>
     * - 异步处理避免线程阻塞，提高系统吞吐量
     * - 智能缓冲减少网络传输次数
     * - 连接状态检测，及时释放无效连接
     *
     * <h3>错误处理：</h3>
     * - 客户端断开连接的优雅处理
     * - API密钥失败的自动重试机制
     * - 详细的错误分类和日志记录
     *
     * @param compatibleApiKey 兼容格式的API密钥，包含用户ID和密钥ID信息
     * @param request OpenAI格式的聊天请求，stream参数应为true
     * @return SseEmitter SSE发射器，用于向客户端发送流式数据
     */
    @Override
    public SseEmitter streamChatCompletions(String compatibleApiKey, OpenAiChatRequest request) {
        log.info("处理OpenAI兼容流式聊天请求 - 模型: {}", request.getModel());

        // 第一步：验证API密钥的有效性
        // 决策理由：流式请求需要立即返回SseEmitter，因此错误处理方式与同步请求不同
        OpenAiApiKeyValidationResult validation = validateCompatibleApiKey(compatibleApiKey);
        if (!validation.isValid()) {
            // 创建错误响应的SseEmitter
            SseEmitter emitter = new SseEmitter(30000L);
            sendErrorAndComplete(emitter, "Invalid API key: " + validation.getMessage());
            return emitter;
        }

        // 第二步：创建SSE发射器，设置较长的超时时间
        // 决策理由：AI模型响应可能需要较长时间，特别是复杂推理任务，设置10分钟超时
        SseEmitter emitter = new SseEmitter(600000L); // 10分钟超时

        // 第三步：启动异步处理流程
        // 决策理由：流式处理需要异步执行，避免阻塞HTTP请求线程
        processStreamChatAsync(emitter, compatibleApiKey, request);

        // 立即返回SseEmitter，客户端可以开始监听事件
        return emitter;
    }

    /**
     * 处理非流式聊天完成请求
     * <p>
     * 这是OpenAI兼容API的核心方法之一，负责处理同步的聊天完成请求。
     * 与流式方法不同，此方法会等待完整响应后一次性返回结果。
     * </p>
     *
     * <h3>核心功能：</h3>
     * <ul>
     *   <li>API密钥验证和解析 - 支持自定义格式的兼容密钥</li>
     *   <li>智能提供商选择 - 根据模型名称自动选择OpenAI/Claude/Gemini</li>
     *   <li>负载均衡 - 在多个API密钥间智能分配请求</li>
     *   <li>格式转换 - OpenAI格式 ↔ 内部适配器格式</li>
     *   <li>多模态支持 - 处理文本+图片的混合内容</li>
     *   <li>推理提取 - 支持o1模型的推理过程提取</li>
     * </ul>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li><strong>密钥验证：</strong>解析兼容格式密钥，验证用户权限和密钥状态</li>
     *   <li><strong>配置获取：</strong>加载用户的AI配置（温度、最大token等默认值）</li>
     *   <li><strong>密钥选择：</strong>根据模型名称和负载情况选择最佳API密钥</li>
     *   <li><strong>请求转换：</strong>将OpenAI格式转换为内部适配器格式</li>
     *   <li><strong>负载标记：</strong>标记API密钥开始使用，用于负载均衡</li>
     *   <li><strong>模型调用：</strong>通过适配器调用实际的AI提供商API</li>
     *   <li><strong>响应转换：</strong>将提供商响应转换回OpenAI兼容格式</li>
     *   <li><strong>状态更新：</strong>更新API密钥使用状态和统计信息</li>
     * </ol>
     *
     * <h3>性能优化：</h3>
     * - 使用Reactor的block()进行同步等待，避免回调地狱
     * - 智能负载均衡减少单个API密钥的压力
     * - 失败快速重试机制提高成功率
     *
     * <h3>错误处理：</h3>
     * - 详细的错误分类和日志记录
     * - API密钥失败统计，支持自动故障转移
     * - 统一的异常包装，保护内部实现细节
     *
     * @param compatibleApiKey 兼容格式的API密钥，包含用户ID和密钥ID信息
     * @param request OpenAI格式的聊天请求，包含模型、消息、参数等
     * @return OpenAiChatResponse OpenAI兼容格式的完整响应，包含生成的消息和使用统计
     * @throws RuntimeException 当API密钥无效、模型不可用或调用失败时
     */
    @Override
    public OpenAiChatResponse chatCompletions(String compatibleApiKey, OpenAiChatRequest request) {
        log.info("处理OpenAI兼容非流式聊天请求 - 模型: {}", request.getModel());

        // 第一步：验证API密钥的有效性和权限
        // 决策理由：优先验证密钥，避免无效请求消耗后续资源
        OpenAiApiKeyValidationResult validation = validateCompatibleApiKey(compatibleApiKey);
        if (!validation.isValid()) {
            throw new RuntimeException("Invalid API key: " + validation.getMessage());
        }

        try {
            // 第二步：获取用户的AI配置信息
            // 决策理由：用户配置包含默认参数，用于补充请求中缺失的参数
            UserAiConfig userConfig = aiConfigService.getUserConfig(validation.getUserId());

            // 第三步：使用简单兼容密钥服务选择合适的API密钥
            // 决策理由：直接通过用户ID和负载均衡选择最佳密钥，无需数据库映射
            SimpleCompatibleKeyService.ApiKeySelectionResult selection =
                simpleCompatibleKeyService.selectApiKeyForModel(compatibleApiKey, request.getModel());
            if (!selection.isSuccess()) {
                throw new RuntimeException("No available API key for model: " + request.getModel() +
                    ". Reason: " + selection.getMessage());
            }

            // 第四步：将OpenAI格式请求转换为内部适配器格式
            // 决策理由：统一的内部格式便于支持多个提供商
            ChatCompletionRequest adapterRequest = convertToAdapterRequest(request, userConfig);

            // 第五步：标记API密钥开始使用（用于负载均衡统计）
            // 决策理由：负载均衡需要实时跟踪每个密钥的使用状态
            loadBalancerService.startUsingApiKey(selection.getSelectedApiKey().getId());

            try {
                // 第六步：调用模型适配服务，获取AI提供商的响应
                // 决策理由：使用block()进行同步等待，确保获得完整响应
                ChatCompletionResponse adapterResponse =
                        modelAdapterService.chatCompletion(selection.getSelectedApiKey().getProvider(), selection.getDecryptedKey(), adapterRequest)
                                .block();

                if (adapterResponse == null) {
                    throw new RuntimeException("No response from model adapter");
                }

                // 第七步：将适配器响应转换为OpenAI兼容格式
                // 决策理由：保持API兼容性，支持所有使用OpenAI API的客户端
                OpenAiChatResponse response = convertToOpenAiResponse(adapterResponse, request.getModel(), selection.getSelectedApiKey().getProvider(), request);

                // 第八步：标记API密钥使用成功，更新负载均衡统计
                loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), true);

                return response;
            } catch (Exception e) {
                // 处理调用失败：标记API密钥失败，用于故障转移决策
                loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), false);
                loadBalancerService.reportApiKeyError(selection.getSelectedApiKey().getId(), e.getMessage());
                throw e;
            }
        } catch (Exception e) {
            log.error("处理非流式聊天请求失败", e);
            throw new RuntimeException("Chat completion failed: " + e.getMessage());
        }
    }

    @Override
    public OpenAiModelResponse listModels(String compatibleApiKey) {
        log.debug("处理OpenAI兼容模型列表请求");

        // 验证API密钥
        OpenAiApiKeyValidationResult validation = validateCompatibleApiKey(compatibleApiKey);
        if (!validation.isValid()) {
            throw new RuntimeException("Invalid API key: " + validation.getMessage());
        }

        try {
            // 获取用户的所有API密钥
            List<UserApiKey> userApiKeys = aiConfigService.getUserApiKeys(validation.getUserId());

            // 构建模型列表
            List<OpenAiModelResponse.ModelData> models = new ArrayList<>();

            for (UserApiKey apiKey : userApiKeys) {
                if (!apiKey.getIsActive()) {
                    continue;
                }

                try {
                    // 获取该提供商的模型列表
                    String decryptedKey = apiKey.getApiKeyEncrypted();
                    List<ModelInfo> providerModels =
                            modelAdapterService.getAvailableModels(apiKey.getProvider(), decryptedKey)
                                    .block();

                    if (providerModels != null) {
                        for (ModelInfo modelInfo : providerModels) {
                            OpenAiModelResponse.ModelData modelData = OpenAiModelResponse.ModelData.builder()
                                    .id(modelInfo.getId())
                                    .object("model")
                                    .created(System.currentTimeMillis() / 1000)
                                    .ownedBy(apiKey.getProvider().name().toLowerCase())
                                    .build();
                            models.add(modelData);
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取提供商{}的模型列表失败", apiKey.getProvider(), e);
                }
            }

            return OpenAiModelResponse.builder()
                    .object("list")
                    .data(models)
                    .build();
        } catch (Exception e) {
            log.error("获取模型列表失败", e);
            throw new RuntimeException("List models failed: " + e.getMessage());
        }
    }

    /**
     * 处理图片生成请求
     * <p>
     * 这是OpenAI兼容API的图片生成核心方法，支持多个AI提供商的图片生成服务。
     * 该方法会根据模型名称自动选择合适的提供商，并处理不同提供商之间的
     * 参数差异和响应格式转换。
     * </p>
     *
     * <h3>支持的提供商和模型：</h3>
     * <ul>
     *   <li><strong>OpenAI:</strong>
     *     <ul>
     *       <li>dall-e-2 - 经典的DALL-E 2模型</li>
     *       <li>dall-e-3 - 最新的DALL-E 3模型，支持更高质量</li>
     *       <li>gpt-image-1 - GPT集成的图片生成模型</li>
     *     </ul>
     *   </li>
     *   <li><strong>Google:</strong>
     *     <ul>
     *       <li>imagen-3.0-generate-001 - Imagen 3.0标准版</li>
     *       <li>imagen-3.0-fast-generate-001 - Imagen 3.0快速版</li>
     *     </ul>
     *   </li>
     *   <li><strong>不支持:</strong> Claude系列（Anthropic不提供图片生成功能）</li>
     * </ul>
     *
     * <h3>处理流程：</h3>
     * <ol>
     *   <li><strong>密钥验证：</strong>验证兼容格式API密钥的有效性</li>
     *   <li><strong>兼容性检查：</strong>验证模型是否支持图片生成功能</li>
     *   <li><strong>提供商选择：</strong>根据模型名称选择对应的AI提供商</li>
     *   <li><strong>参数验证：</strong>检查prompt、size、quality等参数的合法性</li>
     *   <li><strong>负载标记：</strong>标记API密钥开始使用，用于负载均衡</li>
     *   <li><strong>图片生成：</strong>调用对应提供商的图片生成API</li>
     *   <li><strong>响应处理：</strong>转换为OpenAI兼容的响应格式</li>
     *   <li><strong>状态更新：</strong>更新API密钥使用状态和统计信息</li>
     * </ol>
     *
     * <h3>性能考虑：</h3>
     * - 图片生成通常需要10-60秒，使用同步处理确保完整响应
     * - 智能负载均衡避免单个API密钥过载
     * - 失败重试机制提高生成成功率
     *
     * <h3>安全限制：</h3>
     * - 严格的prompt内容过滤，防止生成不当内容
     * - 图片尺寸和数量限制，防止资源滥用
     * - 提供商权限验证，确保API密钥有图片生成权限
     *
     * @param compatibleApiKey 兼容格式的API密钥，包含用户ID和密钥ID信息
     * @param request OpenAI格式的图片生成请求，包含prompt、模型、尺寸等参数
     * @return OpenAiImageResponse OpenAI兼容格式的图片响应，包含生成的图片URL和元数据
     * @throws RuntimeException 当API密钥无效、模型不支持或生成失败时
     */
    @Override
    public OpenAiImageResponse generateImages(String compatibleApiKey, OpenAiImageRequest request) {
        log.info("处理OpenAI兼容图片生成请求 - 模型: {}", request.getModel());

        // 第一步：验证API密钥的有效性和权限
        // 决策理由：图片生成成本较高，优先验证密钥避免无效请求
        OpenAiApiKeyValidationResult validation = validateCompatibleApiKey(compatibleApiKey);
        if (!validation.isValid()) {
            throw new RuntimeException("Invalid API key: " + validation.getMessage());
        }

        // 第二步：验证模型和参数的兼容性
        // 决策理由：不同模型支持的参数不同，需要提前验证避免调用失败
        try {
            request.validateModelCompatibility();
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Model compatibility error: " + e.getMessage());
        }

        // 第三步：使用简单兼容密钥服务选择合适的API密钥
        // 决策理由：图片生成模型与提供商强绑定，需要精确匹配
        SimpleCompatibleKeyService.ApiKeySelectionResult selection =
            simpleCompatibleKeyService.selectApiKeyForModel(compatibleApiKey, request.getModel());
        if (!selection.isSuccess()) {
            throw new RuntimeException("No available API key for model: " + request.getModel() +
                ". Reason: " + selection.getMessage());
        }

        // 第四步：检查提供商是否支持图片生成（排除Claude）
        // 决策理由：Claude不提供图片生成功能，需要明确拒绝此类请求
        if (selection.getSelectedApiKey().getProvider() == UserApiKey.ProviderType.ANTHROPIC) {
            throw new RuntimeException("Claude does not support image generation");
        }

        // 第五步：标记API密钥开始使用（用于负载均衡统计）
        // 决策理由：图片生成耗时较长，需要准确跟踪密钥使用状态
        loadBalancerService.startUsingApiKey(selection.getSelectedApiKey().getId());

        try {
            // 第六步：调用模型适配服务生成图片
            // 决策理由：使用block()同步等待，确保获得完整的图片生成结果
            OpenAiImageResponse response = modelAdapterService.generateImage(
                    selection.getSelectedApiKey().getProvider(),
                    selection.getDecryptedKey(),
                    request
            ).block();

            if (response == null) {
                throw new RuntimeException("No response from model adapter");
            }

            // 第七步：标记API密钥使用成功，更新负载均衡统计
            loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), true);

            return response;
        } catch (Exception e) {
            // 处理生成失败：标记API密钥失败，用于故障转移决策
            loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), false);
            loadBalancerService.reportApiKeyError(selection.getSelectedApiKey().getId(), e.getMessage());
            throw e;
        }
    }

    @Override
    public OpenAiApiKeyValidationResult validateCompatibleApiKey(String compatibleApiKey) {
        try {
            // 使用简单兼容密钥服务验证（基于OpenAI格式）
            SimpleCompatibleKeyService.CompatibleKeyValidationResult validation =
                simpleCompatibleKeyService.validateCompatibleKey(compatibleApiKey);

            if (!validation.isValid()) {
                return new OpenAiApiKeyValidationResult(false, null, null, validation.getMessage());
            }

            // 返回验证结果，keyId设为null因为不使用数据库存储
            return new OpenAiApiKeyValidationResult(true, validation.getUserId(), null, "Valid");
        } catch (Exception e) {
            log.error("验证兼容API密钥失败", e);
            return new OpenAiApiKeyValidationResult(false, null, null, "Validation error: " + e.getMessage());
        }
    }

    // ========================
    // 私有方法
    // ========================

    /**
     * 异步处理流式聊天请求
     */
    @Async("ioIntensiveTaskExecutor")
    public CompletableFuture<Void> processStreamChatAsync(SseEmitter emitter, String compatibleApiKey, OpenAiChatRequest request) {
        try {
            log.info("开始异步处理流式聊天 - 兼容密钥: {}", compatibleApiKey.substring(0, Math.min(20, compatibleApiKey.length())) + "...");

            // 验证兼容密钥并获取用户ID
            SimpleCompatibleKeyService.CompatibleKeyValidationResult validation =
                simpleCompatibleKeyService.validateCompatibleKey(compatibleApiKey);
            if (!validation.isValid()) {
                sendErrorAndComplete(emitter, "Invalid API key: " + validation.getMessage());
                return CompletableFuture.completedFuture(null);
            }

            Long userId = validation.getUserId();

            // 获取用户配置
            UserAiConfig userConfig = aiConfigService.getUserConfig(userId);

            // 选择API密钥和提供商
            SimpleCompatibleKeyService.ApiKeySelectionResult selection =
                simpleCompatibleKeyService.selectApiKeyForModel(compatibleApiKey, request.getModel());
            if (!selection.isSuccess()) {
                sendErrorAndComplete(emitter, "No available API key for model: " + request.getModel() +
                    ". Reason: " + selection.getMessage());
                return CompletableFuture.completedFuture(null);
            }

            // 转换请求格式
            ChatCompletionRequest adapterRequest = convertToAdapterRequest(request, userConfig);
            adapterRequest.setStream(true);

            // 开始使用API密钥
            loadBalancerService.startUsingApiKey(selection.getSelectedApiKey().getId());

            // 生成响应ID
            final String responseId = "chatcmpl-" + UUID.randomUUID().toString().substring(0, 8);

            // 决策理由：使用原子标志防止LoadBalancer双重完成的线程安全问题
            final AtomicBoolean apiKeyFinished = new AtomicBoolean(false);

            try {
                // 发送开始事件
                sendStreamEvent(emitter, OpenAiStreamResponse.createStart(responseId, request.getModel()));

                // 调用模型适配服务获取流式响应，使用http的SSE要用doOnSubscribe异步，普通http请求用.block
                modelAdapterService.streamChatCompletion(selection.getSelectedApiKey().getProvider(), selection.getDecryptedKey(), adapterRequest)
                        // 决策理由：添加背压控制，防止内存溢出和数据丢失
                        .onBackpressureBuffer(1000) // 最多缓冲1000个chunk
                        .doOnSubscribe(subscription -> {
                            log.info("开始流式聊天 - 响应ID: {}, 提供商: {}, 模型: {}",
                                    responseId, selection.getSelectedApiKey().getProvider(), request.getModel());
                        })
                        // 处理接收的数据
                        .doOnNext(chunk -> {
                            try {
                                // 决策理由：所有OpenAI兼容提供商都返回包含思考过程的原始数据，直接转发即可
                                if ("RAWDATA".equals(chunk.getId())) {
                                    String rawJsonData = chunk.getObject(); // 原始JSON数据存储在object字段中

                                    // 对于Claude，需要转换Function Calling格式
                                    if (selection.getSelectedApiKey().getProvider() == UserApiKey.ProviderType.ANTHROPIC) {
                                        String convertedData = convertClaudeStreamData(rawJsonData);
                                        log.debug("Claude流式数据转换 - 原始长度: {}, 转换后长度: {}, 响应ID: {}",
                                                rawJsonData.length(), convertedData.length(), responseId);
                                        sendRawStreamData(emitter, convertedData);
                                    } else {
                                        // OpenAI和Gemini直接转发原始数据
                                        log.debug("{}直接转发原始数据 - 长度: {}, 响应ID: {}",
                                                selection.getSelectedApiKey().getProvider(), rawJsonData.length(), responseId);
                                        sendRawStreamData(emitter, rawJsonData);
                                    }
                                } else {
                                    // 决策理由：理论上不应该到达这里，因为所有提供商都返回原始数据
                                    log.warn("收到非原始数据格式的chunk - ID: {}, 提供商: {}, 响应ID: {}",
                                            chunk.getId(), selection.getSelectedApiKey().getProvider(), responseId);
                                }
                            } catch (Exception e) {
                                log.error("发送流式响应失败 - 响应ID: {}, 错误: {}", responseId, e.getMessage(), e);
                            }
                        })
                        .doOnComplete(() -> {
                            try {
                                log.info("流式响应正常完成 - 响应ID: {}, 模型: {}", responseId, request.getModel());

                                // 发送结束事件
                                OpenAiChatResponse.Usage usage = OpenAiChatResponse.Usage.builder()
                                        .promptTokens(0)
                                        .completionTokens(0)
                                        .totalTokens(0)
                                        .build();
                                sendStreamEvent(emitter, OpenAiStreamResponse.createEnd(responseId, request.getModel(), usage));
                                sendDoneEvent(emitter);
                                safeCompleteEmitter(emitter, responseId);

                                // 决策理由：使用原子操作防止LoadBalancer双重完成，第一次compareAndSet会使用true，后续会使用false
                                if (apiKeyFinished.compareAndSet(false, true)) {
                                    loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), true);
                                    log.info("流式聊天完成 - 响应ID: {}, API密钥ID: {}", responseId, selection.getSelectedApiKey().getId());
                                } else {
                                    log.debug("API密钥已标记完成，跳过重复操作 - 响应ID: {}", responseId);
                                }
                            } catch (Exception e) {
                                log.error("完成流式响应失败 - 响应ID: {}, 错误: {}", responseId, e.getMessage(), e);
                            }
                        })
                        .doOnError(error -> {
                            log.error("流式响应处理失败 - 响应ID: {}, 提供商: {}, 模型: {}, 错误类型: {}, 错误信息: {}",
                                    responseId, selection.getSelectedApiKey().getProvider(), request.getModel(),
                                    error.getClass().getSimpleName(), error.getMessage(), error);

                            // 决策理由：根据错误类型提供更详细的错误信息
                            String errorMessage = "Stream processing failed: " + error.getMessage();
                            if (error instanceof java.util.concurrent.TimeoutException) {
                                errorMessage = "Stream timeout - the AI model response took too long";
                            } else if (error instanceof java.net.ConnectException) {
                                errorMessage = "Connection failed to AI provider";
                            } else if (error instanceof java.io.IOException) {
                                errorMessage = "Network error during streaming";
                            }

                            sendErrorAndComplete(emitter, errorMessage);

                            // 决策理由：使用原子操作防止LoadBalancer双重()完成
                            if (apiKeyFinished.compareAndSet(false, true)) {
                                loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), false);
                                loadBalancerService.reportApiKeyError(selection.getSelectedApiKey().getId(), error.getMessage());
                                log.debug("API密钥标记失败 - 响应ID: {}, 错误: {}", responseId, error.getMessage());
                            } else {
                                log.debug("API密钥已标记完成，跳过错误处理 - 响应ID: {}", responseId);
                            }
                        })
                        .subscribe();

            } catch (Exception e) {
                log.error("启动流式响应失败", e);
                sendErrorAndComplete(emitter, "Failed to start stream: " + e.getMessage());

                // 决策理由：使用原子操作防止LoadBalancer双重完成
                if (apiKeyFinished.compareAndSet(false, true)) {
                    loadBalancerService.finishUsingApiKey(selection.getSelectedApiKey().getId(), false);
                    loadBalancerService.reportApiKeyError(selection.getSelectedApiKey().getId(), e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("异步处理流式聊天失败", e);
            sendErrorAndComplete(emitter, "Internal error: " + e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 从兼容密钥映射中选择适合指定模型的API密钥
     */
    private ApiKeySelection selectApiKeyFromCompatibleKey(Long compatibleKeyId, String model) {
        try {
            // 根据模型名称确定提供商
            UserApiKey.ProviderType provider = determineProviderByModel(model);

            // 获取兼容密钥的映射关系
            List<CompatibleKeyMappingMapper.MappedApiKeyInfo> mappedKeys =
                compatibleKeyMappingMapper.selectMappedApiKeys(compatibleKeyId);

            if (mappedKeys.isEmpty()) {
                log.warn("兼容密钥没有关联的API密钥 - 兼容密钥ID: {}", compatibleKeyId);
                return null;
            }

            // 筛选出匹配提供商且激活的密钥
            List<CompatibleKeyMappingMapper.MappedApiKeyInfo> availableKeys = mappedKeys.stream()
                .filter(key -> provider.name().equals(key.getProvider()))
                .filter(key -> key.getIsActive() && key.getMappingActive())
                .collect(Collectors.toList());

            if (availableKeys.isEmpty()) {
                log.warn("没有找到匹配提供商的可用API密钥 - 兼容密钥ID: {}, 提供商: {}",
                        compatibleKeyId, provider);
                return null;
            }

            // 根据权重和健康状态选择最佳密钥
            CompatibleKeyMappingMapper.MappedApiKeyInfo selectedKey = selectBestMappedKey(availableKeys);
            if (selectedKey == null) {
                return null;
            }

            // 创建UserApiKey对象
            UserApiKey apiKey = new UserApiKey()
                .setId(selectedKey.getId())
                .setUserId(selectedKey.getUserId())
                .setProvider(UserApiKey.ProviderType.valueOf(selectedKey.getProvider()))
                .setKeyName(selectedKey.getKeyName())
                .setApiKeyEncrypted(selectedKey.getApiKeyEncrypted())
                .setIsActive(selectedKey.getIsActive())
                .setPriority(selectedKey.getPriority())
                .setUsageCount(selectedKey.getUsageCount())
                .setLastUsedAt(selectedKey.getLastUsedAt())
                .setCreatedAt(selectedKey.getCreatedAt());

            // 解密API密钥
            String decryptedKey = encryptionUtil.decrypt(selectedKey.getApiKeyEncrypted());
            return new ApiKeySelection(apiKey, decryptedKey);
        } catch (Exception e) {
            log.error("从兼容密钥选择API密钥失败 - 兼容密钥ID: {}, 模型: {}", compatibleKeyId, model, e);
            return null;
        }
    }

    /**
     * 选择适合指定模型的API密钥（保留用于向后兼容）
     * @deprecated 请使用 selectApiKeyFromCompatibleKey
     */
    @Deprecated
    private ApiKeySelection selectApiKeyForModel(Long userId, String model) {
        // 根据模型名称确定提供商
        UserApiKey.ProviderType provider = determineProviderByModel(model);

        // 选择最佳API密钥
        UserApiKey apiKey = loadBalancerService.selectBestApiKey(userId, provider);
        if (apiKey == null) {
            return null;
        }

        // 解密API密钥
        String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());
        return new ApiKeySelection(apiKey, decryptedKey);
    }

    /**
     * 从映射的密钥中选择最佳密钥
     * 基于权重和健康状态进行选择
     */
    private CompatibleKeyMappingMapper.MappedApiKeyInfo selectBestMappedKey(
            List<CompatibleKeyMappingMapper.MappedApiKeyInfo> availableKeys) {

        // 按权重排序，权重高的优先
        availableKeys.sort((a, b) -> Integer.compare(b.getWeight(), a.getWeight()));

        // 优先选择健康的密钥
        for (CompatibleKeyMappingMapper.MappedApiKeyInfo key : availableKeys) {
            try {
                LoadBalancerService.LoadBalanceStats stats =
                    loadBalancerService.getLoadBalanceStats(key.getId());
                if (stats != null && stats.getIsHealthy()) {
                    return key;
                }
            } catch (Exception e) {
                log.warn("获取API密钥健康状态失败 - ID: {}", key.getId(), e);
            }
        }

        // 如果没有健康的密钥，返回权重最高的
        return availableKeys.isEmpty() ? null : availableKeys.get(0);
    }

    /**
     * 根据模型名称确定提供商
     * <p>
     * 决策理由：支持更多模型名称格式，包括最新的 Claude 4 和 Gemini 2.0 模型，以及图片生成模型
     * </p>
     */
    private UserApiKey.ProviderType determineProviderByModel(String model) {
        if (model.startsWith("gpt-") || model.startsWith("text-") || model.startsWith("davinci") || model.startsWith("o1-") ||
            model.startsWith("dall-e-") || model.equals("gpt-image-1")) {
            // 支持聊天模型和图片生成模型
            return UserApiKey.ProviderType.OPENAI;
        } else if (model.startsWith("claude-") || model.contains("claude")) {
            // 支持 claude-3-sonnet, claude-opus-4-20250514 等格式
            return UserApiKey.ProviderType.ANTHROPIC;
        } else if (model.startsWith("gemini-") || model.contains("gemini") || model.startsWith("imagen-")) {
            // 支持 gemini-pro, gemini-1.5-pro, gemini-2.0-flash-exp 以及 imagen 图片生成模型
            return UserApiKey.ProviderType.GOOGLE;
        } else {
            // 默认使用OpenAI
            log.warn("未识别的模型名称: {}，默认使用 OpenAI 提供商", model);
            return UserApiKey.ProviderType.OPENAI;
        }
    }

    /**
     * 转换为适配器请求格式（支持多模态）
     */
    private ChatCompletionRequest convertToAdapterRequest(OpenAiChatRequest request, UserAiConfig userConfig) {
        ChatCompletionRequest adapterRequest = new ChatCompletionRequest();

        adapterRequest.setModel(request.getModel());
        adapterRequest.setTemperature(request.getTemperature() != null ? request.getTemperature() :
                (userConfig.getDefaultTemperature() != null ? userConfig.getDefaultTemperature().doubleValue() : 0.7));
        adapterRequest.setMaxTokens(request.getMaxTokens() != null ? request.getMaxTokens() : userConfig.getDefaultMaxTokens());
        adapterRequest.setTopP(request.getTopP() != null ? request.getTopP() :
                (userConfig.getDefaultTopP() != null ? userConfig.getDefaultTopP().doubleValue() : 1.0));
        adapterRequest.setStream(request.getStream());
        adapterRequest.setSystemPrompt(userConfig.getSystemPrompt());
        adapterRequest.setIncludeReasoning(request.getIncludeReasoning());

        // 添加Function Calling支持
        adapterRequest.setTools(request.getTools());
        adapterRequest.setToolChoice(request.getToolChoice());

        // 添加JSON Output支持
        adapterRequest.setResponseFormat(request.getResponseFormat());

        // 添加流式响应选项支持
        adapterRequest.setStreamOptions(request.getStreamOptions());

        // 转换消息列表（支持多模态）
        List<ChatCompletionRequest.ChatMessage> messages = request.getMessages().stream()
                .map(this::convertToAdapterMessage)
                .collect(Collectors.toList());
        adapterRequest.setMessages(messages);

        return adapterRequest;
    }

    /**
     * 转换单个消息为适配器格式（支持多模态）
     */
    private ChatCompletionRequest.ChatMessage convertToAdapterMessage(OpenAiChatRequest.OpenAiMessage openAiMessage) {
        List<OpenAiChatRequest.ContentPart> content = openAiMessage.getContent();

        if (content == null || content.isEmpty()) {
            throw new IllegalArgumentException("消息内容不能为空");
        }

        // 验证多模态内容
        MultimodalContentUtils.ValidationResult validation =
            MultimodalContentUtils.validateMultimodalContent(content);

        if (!validation.isValid()) {
            log.warn("多模态内容验证失败: {}", validation.getErrorMessage());
            throw new IllegalArgumentException("Invalid multimodal content: " + validation.getErrorMessage());
        }

        // 记录内容统计信息
        MultimodalContentUtils.logContentStats(content);

        // 检查是否包含图片
        if (MultimodalContentUtils.containsImages(content)) {
            log.info("检测到多模态消息，包含 {} 张图片",
                MultimodalContentUtils.getImageCount(content));

            // 对于多模态内容，直接传递原始content对象
            // 让ModelAdapterService处理具体的提供商格式转换
            return new ChatCompletionRequest.ChatMessage(openAiMessage.getRole(), content);
        } else {
            // 纯文本消息，提取文本内容
            String textContent = MultimodalContentUtils.extractTextFromMultimodal(content);
            return new ChatCompletionRequest.ChatMessage(openAiMessage.getRole(), textContent);
        }
    }

    /**
     * 转换为OpenAI响应格式
     */
    private OpenAiChatResponse convertToOpenAiResponse(ChatCompletionResponse adapterResponse, String model, UserApiKey.ProviderType provider, OpenAiChatRequest request) {
        // 决策理由：检查是否为原始数据，如果是则直接解析并返回
        if ("RAWDATA".equals(adapterResponse.getId())) {
            return handleRawDataResponse(adapterResponse.getObject(), model, provider);
        }

        OpenAiChatResponse.Choice choice = OpenAiChatResponse.Choice.builder()
                .index(0)
                .message(new OpenAiChatRequest.OpenAiMessage())
                .finishReason("stop")
                .build();

        if (adapterResponse.getChoices() != null && !adapterResponse.getChoices().isEmpty()) {
            ChatCompletionResponse.Choice adapterChoice = adapterResponse.getChoices().get(0);
            choice.getMessage().setRole("assistant");

            // 将ChatMessage的content转换为ContentPart列表
            Object adapterContent = adapterChoice.getMessage().getContent();
            if (adapterContent instanceof String) {
                // 纯文本响应，转换为ContentPart格式
                List<OpenAiChatRequest.ContentPart> contentParts = new ArrayList<>();
                OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
                textPart.setType("text");
                textPart.setText((String) adapterContent);
                contentParts.add(textPart);
                choice.getMessage().setContent(contentParts);
            } else {
                // 多模态响应，直接设置
                choice.getMessage().setContent((List<OpenAiChatRequest.ContentPart>) adapterContent);
            }

            choice.setFinishReason(adapterChoice.getFinishReason());

            // 决策理由：提取推理过程以支持OpenAI o1兼容格式
            String reasoning = reasoningExtractorService.extractReasoning(provider, adapterResponse, request);
            if (reasoning != null) {
                choice.setReasoningContent(reasoning);
                log.debug("提取到推理过程 - 提供商: {}, 长度: {}", provider, reasoning.length());
            }
        }

        OpenAiChatResponse.Usage usage = OpenAiChatResponse.Usage.builder()
                .promptTokens(adapterResponse.getUsage() != null ? adapterResponse.getUsage().getPromptTokens() : 0)
                .completionTokens(adapterResponse.getUsage() != null ? adapterResponse.getUsage().getCompletionTokens() : 0)
                .totalTokens(adapterResponse.getUsage() != null ? adapterResponse.getUsage().getTotalTokens() : 0)
                .build();

        return OpenAiChatResponse.builder()
                .id(adapterResponse.getId() != null ? adapterResponse.getId() : "chatcmpl-" + UUID.randomUUID().toString().substring(0, 8))
                .object("chat.completion")
                .created(adapterResponse.getCreated() != null ? adapterResponse.getCreated() : System.currentTimeMillis() / 1000)
                .model(model)
                .choices(List.of(choice))
                .usage(usage)
                .build();
    }

    /**
     * 发送SSE事件
     */
    private void sendStreamEvent(SseEmitter emitter, OpenAiStreamResponse response) {
        // 决策理由：添加同步锁防止并发访问同一个 SseEmitter
        synchronized (emitter) {
            try {
                String jsonData = objectMapper.writeValueAsString(response);

                // 决策理由：记录发送的数据大小以便监控传输性能
                log.debug("发送SSE事件 - 响应ID: {}, 数据大小: {} bytes", response.getId(), jsonData.length());

                emitter.send(SseEmitter.event().data(jsonData));
            } catch (IllegalStateException e) {
                // 决策理由：专门处理 SseEmitter 已完成的情况，这是正常的竞态条件
                if (e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                    log.debug("SseEmitter已完成，跳过发送 - 响应ID: {}, 原因: 客户端可能已断开连接或流已结束", response.getId());
                } else {
                    log.warn("SseEmitter状态异常 - 响应ID: {}, 错误: {}", response.getId(), e.getMessage());
                }
            } catch (IOException e) {
                // 决策理由：区分客户端断开连接和真正的系统错误，减少日志噪音
                if (e instanceof org.apache.catalina.connector.ClientAbortException ||
                    e.getMessage().contains("An established connection was aborted") ||
                    e.getMessage().contains("Broken pipe") ||
                    e.getMessage().contains("Connection reset")) {
                    log.debug("客户端断开SSE连接 - 响应ID: {}, 原因: {}", response.getId(), e.getMessage());
                } else {
                    log.error("发送SSE事件失败 - 响应ID: {}, 错误: {}", response.getId(), e.getMessage(), e);
                }
            } catch (Exception e) {
                log.error("发送SSE事件时发生未预期错误 - 响应ID: {}, 错误: {}", response.getId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 直接发送原始流式数据（用于OpenAI提供商优化）
     * <p>
     * 决策理由：对于OpenAI提供商，直接转发原始JSON数据，避免解析和重构造的性能损耗
     * </p>
     */
    private void sendRawStreamData(SseEmitter emitter, String rawJsonData) {
        // 决策理由：添加同步锁防止并发访问同一个 SseEmitter
        synchronized (emitter) {
            try {
                // 决策理由：记录发送的原始数据大小以便监控传输性能
                log.debug("发送原始SSE数据 - 数据大小: {} bytes", rawJsonData.length());

                // 直接发送原始JSON数据，无需额外的序列化
                emitter.send(SseEmitter.event().data(rawJsonData));
            } catch (IllegalStateException e) {
                // 决策理由：专门处理 SseEmitter 已完成的情况，这是正常的竞态条件
                if (e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                    log.debug("SseEmitter已完成，跳过发送原始数据，原因: 客户端可能已断开连接或流已结束");
                } else {
                    log.warn("SseEmitter状态异常，无法发送原始数据: {}", e.getMessage());
                }
            } catch (IOException e) {
                // 决策理由：区分客户端断开连接和真正的系统错误，减少日志噪音
                if (e instanceof org.apache.catalina.connector.ClientAbortException ||
                    e.getMessage().contains("An established connection was aborted") ||
                    e.getMessage().contains("Broken pipe") ||
                    e.getMessage().contains("Connection reset")) {
                    log.debug("客户端断开SSE连接（原始数据发送）: {}", e.getMessage());
                } else {
                    log.error("发送原始SSE数据失败: {}", e.getMessage(), e);
                }
            } catch (Exception e) {
                log.error("发送原始SSE数据时发生未预期错误: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 发送完成事件
     */
    private void sendDoneEvent(SseEmitter emitter) {
        // 决策理由：添加同步锁防止并发访问同一个 SseEmitter
        synchronized (emitter) {
            try {
                log.debug("发送SSE完成事件: [DONE]");
                emitter.send(SseEmitter.event().data("[DONE]"));
            } catch (IllegalStateException e) {
                // 决策理由：专门处理 SseEmitter 已完成的情况
                if (e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                    log.debug("SseEmitter已完成，跳过发送完成事件，原因: 客户端可能已断开连接或流已结束");
                } else {
                    log.warn("SseEmitter状态异常，无法发送完成事件: {}", e.getMessage());
                }
            } catch (IOException e) {
                // 决策理由：区分客户端断开连接和真正的系统错误
                if (e instanceof org.apache.catalina.connector.ClientAbortException ||
                    e.getMessage().contains("An established connection was aborted") ||
                    e.getMessage().contains("Broken pipe") ||
                    e.getMessage().contains("Connection reset")) {
                    log.debug("客户端在完成事件发送前断开连接: {}", e.getMessage());
                } else {
                    log.error("发送完成事件失败: {}", e.getMessage(), e);
                }
            } catch (Exception e) {
                log.error("发送完成事件时发生未预期错误: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 安全地完成 SseEmitter
     */
    private void safeCompleteEmitter(SseEmitter emitter, String responseId) {
        synchronized (emitter) {
            try {
                emitter.complete();
                log.debug("SseEmitter安全完成 - 响应ID: {}", responseId);
            } catch (IllegalStateException e) {
                if (e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                    log.debug("SseEmitter已经完成 - 响应ID: {}", responseId);
                } else {
                    log.warn("SseEmitter完成时状态异常 - 响应ID: {}, 错误: {}", responseId, e.getMessage());
                }
            } catch (Exception e) {
                log.error("SseEmitter完成时发生未预期错误 - 响应ID: {}, 错误: {}", responseId, e.getMessage(), e);
            }
        }
    }

    /**
     * 发送错误并完成
     */
    private void sendErrorAndComplete(SseEmitter emitter, String errorMessage) {
        synchronized (emitter) {
            try {
                emitter.send(SseEmitter.event().data("{\"error\": \"" + errorMessage + "\"}"));
                emitter.completeWithError(new RuntimeException(errorMessage));
            } catch (IllegalStateException e) {
                if (e.getMessage().contains("ResponseBodyEmitter has already completed")) {
                    log.debug("SseEmitter已完成，跳过错误发送: {}", errorMessage);
                } else {
                    log.warn("SseEmitter状态异常，无法发送错误: {}, 原始错误: {}", e.getMessage(), errorMessage);
                }
            } catch (IOException e) {
                log.error("发送错误事件失败: {}, 原始错误: {}", e.getMessage(), errorMessage);
                try {
                    emitter.completeWithError(e);
                } catch (IllegalStateException ise) {
                    log.debug("SseEmitter已完成，无法设置错误状态");
                }
            } catch (Exception e) {
                log.error("发送错误时发生未预期异常: {}, 原始错误: {}", e.getMessage(), errorMessage, e);
                try {
                    emitter.completeWithError(e);
                } catch (IllegalStateException ise) {
                    log.debug("SseEmitter已完成，无法设置错误状态");
                }
            }
        }
    }

    /**
     * 处理原始数据响应，支持Function Calling格式转换
     */
    private OpenAiChatResponse handleRawDataResponse(String rawJsonData, String model, UserApiKey.ProviderType provider) {
        try {
            // 解析原始JSON数据
            JsonNode jsonNode = objectMapper.readTree(rawJsonData);

            // 对于Claude，需要转换Function Calling格式
            if (provider == UserApiKey.ProviderType.ANTHROPIC) {
                return convertClaudeResponseToOpenAi(jsonNode, model);
            } else {
                // OpenAI和Gemini格式相同，直接解析
                return objectMapper.treeToValue(jsonNode, OpenAiChatResponse.class);
            }
        } catch (Exception e) {
            log.error("解析原始响应数据失败: {}", rawJsonData, e);
            // 返回错误响应
            return createErrorResponse(model, "Failed to parse response: " + e.getMessage());
        }
    }

    /**
     * 将Claude响应转换为OpenAI格式（特别处理Function Calling）
     */
    private OpenAiChatResponse convertClaudeResponseToOpenAi(JsonNode claudeResponse, String model) {
        try {
            // 检查是否包含content数组
            JsonNode contentArray = claudeResponse.get("content");
            if (contentArray != null && contentArray.isArray()) {
                // 使用通用的内容转换逻辑
                ClaudeContentConversion conversion = convertClaudeContentToOpenAi(contentArray);

                // 只有包含Function Calling时才进行转换
                if (conversion.hasToolCalls) {
                    OpenAiChatResponse.Choice choice = OpenAiChatResponse.Choice.builder()
                            .index(0)
                            .message(new OpenAiChatRequest.OpenAiMessage())
                            .finishReason("tool_calls")
                            .build();

                    choice.getMessage().setRole("assistant");
                    choice.getMessage().setContent(conversion.contentParts);
                    choice.getMessage().setToolCalls(conversion.toolCalls);

                    // 处理usage信息
                    OpenAiChatResponse.Usage usage = OpenAiChatResponse.Usage.builder()
                            .promptTokens(claudeResponse.path("usage").path("input_tokens").asInt(0))
                            .completionTokens(claudeResponse.path("usage").path("output_tokens").asInt(0))
                            .totalTokens(claudeResponse.path("usage").path("input_tokens").asInt(0) +
                                       claudeResponse.path("usage").path("output_tokens").asInt(0))
                            .build();

                    return OpenAiChatResponse.builder()
                            .id(claudeResponse.path("id").asText("chatcmpl-" + UUID.randomUUID().toString().substring(0, 8)))
                            .object("chat.completion")
                            .created(System.currentTimeMillis() / 1000)
                            .model(model)
                            .choices(List.of(choice))
                            .usage(usage)
                            .build();
                }
            }

            // 如果不包含Function Calling，直接解析为OpenAI格式
            return objectMapper.treeToValue(claudeResponse, OpenAiChatResponse.class);

        } catch (Exception e) {
            log.error("转换Claude响应失败", e);
            return createErrorResponse(model, "Failed to convert Claude response: " + e.getMessage());
        }
    }

    /**
     * 创建错误响应
     */
    private OpenAiChatResponse createErrorResponse(String model, String errorMessage) {
        OpenAiChatResponse.Choice choice = OpenAiChatResponse.Choice.builder()
                .index(0)
                .message(new OpenAiChatRequest.OpenAiMessage())
                .finishReason("stop")
                .build();

        choice.getMessage().setRole("assistant");

        List<OpenAiChatRequest.ContentPart> contentParts = new ArrayList<>();
        OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
        textPart.setType("text");
        textPart.setText("Error: " + errorMessage);
        contentParts.add(textPart);
        choice.getMessage().setContent(contentParts);

        OpenAiChatResponse.Usage usage = OpenAiChatResponse.Usage.builder()
                .promptTokens(0)
                .completionTokens(0)
                .totalTokens(0)
                .build();

        return OpenAiChatResponse.builder()
                .id("chatcmpl-error")
                .object("chat.completion")
                .created(System.currentTimeMillis() / 1000)
                .model(model)
                .choices(List.of(choice))
                .usage(usage)
                .build();
    }

    /**
     * Claude内容转换结果
     */
    private static class ClaudeContentConversion {
        final List<OpenAiChatRequest.ContentPart> contentParts;
        final List<OpenAiChatRequest.ToolCall> toolCalls;
        final boolean hasToolCalls;

        ClaudeContentConversion(List<OpenAiChatRequest.ContentPart> contentParts,
                               List<OpenAiChatRequest.ToolCall> toolCalls) {
            this.contentParts = contentParts;
            this.toolCalls = toolCalls;
            this.hasToolCalls = toolCalls != null && !toolCalls.isEmpty();
        }
    }

    /**
     * 通用的Claude内容转换为OpenAI格式
     */
    private ClaudeContentConversion convertClaudeContentToOpenAi(JsonNode contentArray) {
        List<OpenAiChatRequest.ContentPart> contentParts = new ArrayList<>();
        List<OpenAiChatRequest.ToolCall> toolCalls = new ArrayList<>();

        if (contentArray != null && contentArray.isArray()) {
            for (JsonNode contentItem : contentArray) {
                String type = contentItem.get("type").asText();

                if ("text".equals(type)) {
                    // 文本内容
                    OpenAiChatRequest.ContentPart textPart = new OpenAiChatRequest.ContentPart();
                    textPart.setType("text");
                    textPart.setText(contentItem.get("text").asText());
                    contentParts.add(textPart);
                } else if ("tool_use".equals(type)) {
                    // Claude的tool_use转换为OpenAI的tool_calls
                    try {
                        OpenAiChatRequest.ToolCall toolCall = new OpenAiChatRequest.ToolCall();
                        toolCall.setId(contentItem.get("id").asText());
                        toolCall.setType("function");

                        OpenAiChatRequest.FunctionCall functionCall = new OpenAiChatRequest.FunctionCall();
                        functionCall.setName(contentItem.get("name").asText());

                        // 将input对象转换为JSON字符串
                        JsonNode inputNode = contentItem.get("input");
                        functionCall.setArguments(objectMapper.writeValueAsString(inputNode));

                        toolCall.setFunction(functionCall);
                        toolCalls.add(toolCall);
                    } catch (Exception e) {
                        log.error("转换Claude tool_use失败", e);
                    }
                }
            }
        }

        return new ClaudeContentConversion(contentParts, toolCalls);
    }

    /**
     * 转换Claude流式数据为OpenAI格式（处理Function Calling）
     */
    private String convertClaudeStreamData(String claudeStreamData) {
        try {
            // 解析Claude流式数据
            JsonNode claudeNode = objectMapper.readTree(claudeStreamData);

            // 检查是否包含content数组（Claude格式）
            JsonNode contentArray = claudeNode.get("content");
            if (contentArray != null && contentArray.isArray()) {
                // 使用通用转换逻辑
                ClaudeContentConversion conversion = convertClaudeContentToOpenAi(contentArray);
                if (conversion.hasToolCalls) {
                    return createOpenAiStreamChunk(claudeNode, conversion);
                }
            }

            // 如果不包含tool_use或转换失败，直接返回原始数据
            return claudeStreamData;
        } catch (Exception e) {
            log.error("转换Claude流式数据失败: {}", claudeStreamData, e);
            return claudeStreamData; // 转换失败时返回原始数据
        }
    }

    /**
     * 创建OpenAI格式的流式响应块
     */
    private String createOpenAiStreamChunk(JsonNode claudeNode, ClaudeContentConversion conversion) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode openAiNode = mapper.createObjectNode();

            // 复制基本字段
            openAiNode.put("id", claudeNode.path("id").asText());
            openAiNode.put("object", "chat.completion.chunk");
            openAiNode.put("created", claudeNode.path("created").asLong());
            openAiNode.put("model", claudeNode.path("model").asText());

            // 创建choices数组
            ArrayNode choicesArray = mapper.createArrayNode();
            ObjectNode choice = mapper.createObjectNode();
            choice.put("index", 0);
            choice.put("finish_reason", conversion.hasToolCalls ? "tool_calls" : null);

            // 创建delta对象
            ObjectNode delta = mapper.createObjectNode();
            delta.put("role", "assistant");

            // 如果有工具调用，添加tool_calls
            if (conversion.hasToolCalls) {
                ArrayNode toolCallsArray = mapper.createArrayNode();
                for (OpenAiChatRequest.ToolCall toolCall : conversion.toolCalls) {
                    ObjectNode toolCallNode = mapper.createObjectNode();
                    toolCallNode.put("id", toolCall.getId());
                    toolCallNode.put("type", "function");

                    ObjectNode function = mapper.createObjectNode();
                    function.put("name", toolCall.getFunction().getName());
                    function.put("arguments", toolCall.getFunction().getArguments());

                    toolCallNode.set("function", function);
                    toolCallsArray.add(toolCallNode);
                }
                delta.set("tool_calls", toolCallsArray);
            }

            // 如果有文本内容，添加content
            if (!conversion.contentParts.isEmpty()) {
                StringBuilder contentText = new StringBuilder();
                for (OpenAiChatRequest.ContentPart part : conversion.contentParts) {
                    if ("text".equals(part.getType())) {
                        contentText.append(part.getText());
                    }
                }
                if (contentText.length() > 0) {
                    delta.put("content", contentText.toString());
                }
            }

            choice.set("delta", delta);
            choicesArray.add(choice);
            openAiNode.set("choices", choicesArray);

            return mapper.writeValueAsString(openAiNode);
        } catch (Exception e) {
            log.error("创建OpenAI流式响应块失败", e);
            return ""; // 返回空字符串，避免发送错误数据
        }
    }

}
