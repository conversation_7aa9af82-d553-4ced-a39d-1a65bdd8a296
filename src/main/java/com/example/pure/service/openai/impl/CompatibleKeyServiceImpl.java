package com.example.pure.service.openai.impl;

import com.example.pure.mapper.primary.CompatibleApiKeyMapper;
import com.example.pure.mapper.primary.CompatibleKeyMappingMapper;
import com.example.pure.mapper.primary.UserApiKeyMapper;
import com.example.pure.model.dto.request.openai.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.openai.CompatibleKeyDto;
import com.example.pure.model.entity.CompatibleApiKey;
import com.example.pure.model.entity.CompatibleKeyMapping;
import com.example.pure.model.entity.UserApiKey;
import com.example.pure.service.openai.CompatibleKeyService;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.util.SpringEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 兼容API密钥服务实现类
 * <p>
 * 提供兼容API密钥的管理功能实现
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompatibleKeyServiceImpl implements CompatibleKeyService {

    private final CompatibleApiKeyMapper compatibleApiKeyMapper;
    private final CompatibleKeyMappingMapper compatibleKeyMappingMapper;
    private final UserApiKeyMapper userApiKeyMapper;
    private final SpringEncryptionUtil springEncryptionUtil;
    private final LoadBalancerService loadBalancerService;

    @Override
    @Transactional
    public CompatibleKeyDto createCompatibleKey(Long userId, CreateCompatibleKeyRequest request) {
        log.info("创建兼容API密钥 - 用户ID: {}, 密钥名称: {}", userId, request.getKeyName());

        // 验证关联的API密钥是否属于当前用户
        validateUserApiKeys(userId, request.getApiKeyMappings());

        // 生成兼容密钥
        SpringEncryptionUtil.CompatibleKeyResult keyResult = 
            springEncryptionUtil.generateCompatibleApiKey(userId, request.getKeyName());

        // 创建兼容密钥实体
        CompatibleApiKey compatibleApiKey = new CompatibleApiKey()
                .setUserId(userId)
                .setSnowflakeId(keyResult.getSnowflakeId())
                .setKeyName(request.getKeyName())
                .setCompatibleKey(keyResult.getCompatibleKey())
                .setSecurityHash(keyResult.getSecurityHash())
                .setIsActive(true)
                .setDescription(request.getDescription())
                .setUsageCount(0L)
                .setCreatedAt(Instant.now())
                .setUpdatedAt(Instant.now())
                .setExpiresAt(request.getExpiresAt());

        // 插入兼容密钥
        int inserted = compatibleApiKeyMapper.insert(compatibleApiKey);
        if (inserted == 0) {
            throw new RuntimeException("创建兼容API密钥失败");
        }

        // 创建映射关系
        List<CompatibleKeyMapping> mappings = new ArrayList<>();
        for (CreateCompatibleKeyRequest.ApiKeyMapping mapping : request.getApiKeyMappings()) {
            CompatibleKeyMapping keyMapping = new CompatibleKeyMapping()
                    .setCompatibleKeyId(compatibleApiKey.getId())
                    .setUserApiKeyId(mapping.getUserApiKeyId())
                    .setWeight(mapping.getWeight())
                    .setIsActive(true)
                    .setCreatedAt(Instant.now())
                    .setUpdatedAt(Instant.now());
            mappings.add(keyMapping);
        }

        // 批量插入映射关系
        if (!mappings.isEmpty()) {
            int mappingInserted = compatibleKeyMappingMapper.batchInsert(mappings);
            if (mappingInserted != mappings.size()) {
                throw new RuntimeException("创建API密钥映射失败");
            }
        }

        log.info("兼容API密钥创建成功 - ID: {}, 兼容密钥: {}", 
                compatibleApiKey.getId(), compatibleApiKey.getCompatibleKey());

        return convertToDto(compatibleApiKey);
    }

    @Override
    public List<CompatibleKeyDto> getUserCompatibleKeys(Long userId) {
        List<CompatibleApiKey> compatibleKeys = compatibleApiKeyMapper.selectByUserId(userId);
        return compatibleKeys.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CompatibleKeyDto getCompatibleKeyById(Long userId, Long keyId) {
        CompatibleApiKey compatibleKey = compatibleApiKeyMapper.selectById(keyId);
        if (compatibleKey == null || !compatibleKey.getUserId().equals(userId)) {
            return null;
        }
        return convertToDto(compatibleKey);
    }

    @Override
    public CompatibleApiKey getCompatibleKeyByKey(String compatibleKey) {
        return compatibleApiKeyMapper.selectByCompatibleKey(compatibleKey);
    }

    @Override
    @Transactional
    public CompatibleKeyDto updateCompatibleKey(Long userId, Long keyId, CreateCompatibleKeyRequest request) {
        log.info("更新兼容API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询现有兼容密钥
        CompatibleApiKey existingKey = compatibleApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("兼容API密钥不存在或无权限访问");
        }

        // 验证关联的API密钥
        validateUserApiKeys(userId, request.getApiKeyMappings());

        // 更新兼容密钥信息
        existingKey.setKeyName(request.getKeyName())
                  .setDescription(request.getDescription())
                  .setExpiresAt(request.getExpiresAt())
                  .setUpdatedAt(Instant.now());

        int updated = compatibleApiKeyMapper.updateById(existingKey);
        if (updated == 0) {
            throw new RuntimeException("更新兼容API密钥失败");
        }

        // 删除现有映射关系
        compatibleKeyMappingMapper.deleteByCompatibleKeyId(keyId);

        // 创建新的映射关系
        List<CompatibleKeyMapping> mappings = new ArrayList<>();
        for (CreateCompatibleKeyRequest.ApiKeyMapping mapping : request.getApiKeyMappings()) {
            CompatibleKeyMapping keyMapping = new CompatibleKeyMapping()
                    .setCompatibleKeyId(keyId)
                    .setUserApiKeyId(mapping.getUserApiKeyId())
                    .setWeight(mapping.getWeight())
                    .setIsActive(true)
                    .setCreatedAt(Instant.now())
                    .setUpdatedAt(Instant.now());
            mappings.add(keyMapping);
        }

        // 批量插入新映射关系
        if (!mappings.isEmpty()) {
            int mappingInserted = compatibleKeyMappingMapper.batchInsert(mappings);
            if (mappingInserted != mappings.size()) {
                throw new RuntimeException("更新API密钥映射失败");
            }
        }

        log.info("兼容API密钥更新成功 - ID: {}", keyId);
        return convertToDto(existingKey);
    }

    @Override
    @Transactional
    public boolean deleteCompatibleKey(Long userId, Long keyId) {
        log.info("删除兼容API密钥 - 用户ID: {}, 密钥ID: {}", userId, keyId);

        // 查询现有兼容密钥
        CompatibleApiKey existingKey = compatibleApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            return false;
        }

        // 删除映射关系
        compatibleKeyMappingMapper.deleteByCompatibleKeyId(keyId);

        // 删除兼容密钥
        int deleted = compatibleApiKeyMapper.deleteById(keyId);

        log.info("兼容API密钥删除成功 - ID: {}", keyId);
        return deleted > 0;
    }

    @Override
    @Transactional
    public CompatibleKeyDto toggleCompatibleKey(Long userId, Long keyId, Boolean isActive) {
        log.info("切换兼容API密钥状态 - 用户ID: {}, 密钥ID: {}, 状态: {}", userId, keyId, isActive);

        // 查询现有兼容密钥
        CompatibleApiKey existingKey = compatibleApiKeyMapper.selectById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("兼容API密钥不存在或无权限访问");
        }

        // 更新状态
        existingKey.setIsActive(isActive).setUpdatedAt(Instant.now());
        int updated = compatibleApiKeyMapper.updateById(existingKey);
        if (updated == 0) {
            throw new RuntimeException("更新兼容API密钥状态失败");
        }

        log.info("兼容API密钥状态切换成功 - ID: {}, 状态: {}", keyId, isActive);
        return convertToDto(existingKey);
    }

    @Override
    @Transactional
    public CompatibleKeyDto addApiKeyMapping(Long userId, Long keyId, Long userApiKeyId, Integer weight) {
        log.info("添加API密钥映射 - 用户ID: {}, 兼容密钥ID: {}, API密钥ID: {}", 
                userId, keyId, userApiKeyId);

        // 验证兼容密钥权限
        CompatibleApiKey compatibleKey = compatibleApiKeyMapper.selectById(keyId);
        if (compatibleKey == null || !compatibleKey.getUserId().equals(userId)) {
            throw new RuntimeException("兼容API密钥不存在或无权限访问");
        }

        // 验证API密钥权限
        UserApiKey userApiKey = userApiKeyMapper.selectById(userApiKeyId);
        if (userApiKey == null || !userApiKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限访问");
        }

        // 检查映射是否已存在
        if (compatibleKeyMappingMapper.existsByCompatibleKeyIdAndUserApiKeyId(keyId, userApiKeyId)) {
            throw new RuntimeException("API密钥映射已存在");
        }

        // 创建映射关系
        CompatibleKeyMapping mapping = new CompatibleKeyMapping()
                .setCompatibleKeyId(keyId)
                .setUserApiKeyId(userApiKeyId)
                .setWeight(weight != null ? weight : 10)
                .setIsActive(true)
                .setCreatedAt(Instant.now())
                .setUpdatedAt(Instant.now());

        int inserted = compatibleKeyMappingMapper.insert(mapping);
        if (inserted == 0) {
            throw new RuntimeException("添加API密钥映射失败");
        }

        log.info("API密钥映射添加成功 - 映射ID: {}", mapping.getId());
        return convertToDto(compatibleKey);
    }

    @Override
    @Transactional
    public CompatibleKeyDto removeApiKeyMapping(Long userId, Long keyId, Long mappingId) {
        log.info("移除API密钥映射 - 用户ID: {}, 兼容密钥ID: {}, 映射ID: {}",
                userId, keyId, mappingId);

        // 验证兼容密钥权限
        CompatibleApiKey compatibleKey = compatibleApiKeyMapper.selectById(keyId);
        if (compatibleKey == null || !compatibleKey.getUserId().equals(userId)) {
            throw new RuntimeException("兼容API密钥不存在或无权限访问");
        }

        // 验证映射关系
        CompatibleKeyMapping mapping = compatibleKeyMappingMapper.selectById(mappingId);
        if (mapping == null || !mapping.getCompatibleKeyId().equals(keyId)) {
            throw new RuntimeException("API密钥映射不存在");
        }

        // 删除映射关系
        int deleted = compatibleKeyMappingMapper.deleteById(mappingId);
        if (deleted == 0) {
            throw new RuntimeException("移除API密钥映射失败");
        }

        log.info("API密钥映射移除成功 - 映射ID: {}", mappingId);
        return convertToDto(compatibleKey);
    }

    @Override
    public CompatibleKeyValidationResult validateCompatibleKey(String compatibleKey) {
        try {
            // 解析兼容密钥
            SpringEncryptionUtil.ParseResult parseResult =
                springEncryptionUtil.parseCompatibleApiKey(compatibleKey);

            if (!parseResult.isValid()) {
                return new CompatibleKeyValidationResult(false, null, null, "兼容密钥格式无效");
            }

            // 查询兼容密钥
            CompatibleApiKey apiKey = compatibleApiKeyMapper.selectByCompatibleKey(compatibleKey);
            if (apiKey == null) {
                return new CompatibleKeyValidationResult(false, null, null, "兼容密钥不存在");
            }

            // 验证哈希值
            if (!springEncryptionUtil.validateCompatibleApiKey(compatibleKey, apiKey.getSecurityHash())) {
                return new CompatibleKeyValidationResult(false, null, null, "兼容密钥验证失败");
            }

            // 检查是否激活
            if (!apiKey.getIsActive()) {
                return new CompatibleKeyValidationResult(false, apiKey.getUserId(), apiKey, "兼容密钥已禁用");
            }

            // 检查是否过期
            if (apiKey.getExpiresAt() != null && apiKey.getExpiresAt().isBefore(Instant.now())) {
                return new CompatibleKeyValidationResult(false, apiKey.getUserId(), apiKey, "兼容密钥已过期");
            }

            return new CompatibleKeyValidationResult(true, apiKey.getUserId(), apiKey, "验证成功");
        } catch (Exception e) {
            log.error("验证兼容密钥失败: {}", compatibleKey, e);
            return new CompatibleKeyValidationResult(false, null, null, "验证失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updateUsageStats(String compatibleKey) {
        try {
            CompatibleApiKey apiKey = compatibleApiKeyMapper.selectByCompatibleKey(compatibleKey);
            if (apiKey != null) {
                long newUsageCount = apiKey.getUsageCount() + 1;
                compatibleApiKeyMapper.updateUsageStats(apiKey.getId(), newUsageCount, Instant.now());
            }
        } catch (Exception e) {
            log.warn("更新兼容密钥使用统计失败: {}", compatibleKey, e);
        }
    }

    /**
     * 验证用户API密钥权限
     */
    private void validateUserApiKeys(Long userId, List<CreateCompatibleKeyRequest.ApiKeyMapping> mappings) {
        for (CreateCompatibleKeyRequest.ApiKeyMapping mapping : mappings) {
            UserApiKey userApiKey = userApiKeyMapper.selectById(mapping.getUserApiKeyId());
            if (userApiKey == null || !userApiKey.getUserId().equals(userId)) {
                throw new RuntimeException("API密钥不存在或无权限访问: " + mapping.getUserApiKeyId());
            }
            if (!userApiKey.getIsActive()) {
                throw new RuntimeException("API密钥已禁用: " + mapping.getUserApiKeyId());
            }
        }
    }

    /**
     * 转换为DTO
     */
    private CompatibleKeyDto convertToDto(CompatibleApiKey compatibleKey) {
        CompatibleKeyDto dto = new CompatibleKeyDto();
        dto.setId(compatibleKey.getId());
        dto.setSnowflakeId(compatibleKey.getSnowflakeId());
        dto.setKeyName(compatibleKey.getKeyName());
        dto.setCompatibleKey(compatibleKey.getCompatibleKey());
        dto.setIsActive(compatibleKey.getIsActive());
        dto.setDescription(compatibleKey.getDescription());
        dto.setUsageCount(compatibleKey.getUsageCount());
        dto.setLastUsedAt(compatibleKey.getLastUsedAt());
        dto.setCreatedAt(compatibleKey.getCreatedAt());
        dto.setExpiresAt(compatibleKey.getExpiresAt());

        // 获取映射的API密钥信息
        List<CompatibleKeyMappingMapper.MappedApiKeyInfo> mappedInfos =
            compatibleKeyMappingMapper.selectMappedApiKeys(compatibleKey.getId());

        List<CompatibleKeyDto.MappedApiKeyDto> mappedApiKeys = new ArrayList<>();
        int totalWeight = 0;
        int availableWeight = 0;
        int healthyKeys = 0;
        int unhealthyKeys = 0;

        for (CompatibleKeyMappingMapper.MappedApiKeyInfo info : mappedInfos) {
            CompatibleKeyDto.MappedApiKeyDto mappedDto = new CompatibleKeyDto.MappedApiKeyDto();
            mappedDto.setApiKeyId(info.getId());
            mappedDto.setKeyName(info.getKeyName());
            mappedDto.setProvider(info.getProvider());
            mappedDto.setMaskedApiKey(springEncryptionUtil.maskApiKey(info.getApiKeyEncrypted()));
            mappedDto.setWeight(info.getWeight());
            mappedDto.setIsActive(info.getIsActive() && info.getMappingActive());

            // 获取健康状态
            try {
                LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(info.getId());
                boolean isHealthy = stats != null && stats.getIsHealthy();
                mappedDto.setIsHealthy(isHealthy);

                if (isHealthy) {
                    healthyKeys++;
                } else {
                    unhealthyKeys++;
                }
            } catch (Exception e) {
                log.warn("获取API密钥健康状态失败 - ID: {}", info.getId(), e);
                mappedDto.setIsHealthy(false);
                unhealthyKeys++;
            }

            totalWeight += info.getWeight();
            if (mappedDto.getIsActive() && mappedDto.getIsHealthy()) {
                availableWeight += info.getWeight();
            }

            mappedApiKeys.add(mappedDto);
        }

        dto.setMappedApiKeys(mappedApiKeys);

        // 设置健康状态统计
        CompatibleKeyDto.HealthStats healthStats = new CompatibleKeyDto.HealthStats();
        healthStats.setTotalKeys(mappedApiKeys.size());
        healthStats.setHealthyKeys(healthyKeys);
        healthStats.setUnhealthyKeys(unhealthyKeys);
        healthStats.setTotalWeight(totalWeight);
        healthStats.setAvailableWeight(availableWeight);
        dto.setHealthStats(healthStats);

        return dto;
    }
}
