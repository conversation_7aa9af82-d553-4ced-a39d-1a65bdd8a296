package com.example.pure.service.openai;

import com.example.pure.model.dto.request.openai.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.openai.CompatibleKeyDto;
import com.example.pure.model.entity.CompatibleApiKey;

import java.util.List;

/**
 * 兼容API密钥服务接口
 * <p>
 * 提供兼容API密钥的管理功能
 * </p>
 */
public interface CompatibleKeyService {

    /**
     * 创建兼容API密钥
     *
     * @param userId 用户ID
     * @param request 创建请求
     * @return 兼容密钥DTO
     */
    CompatibleKeyDto createCompatibleKey(Long userId, CreateCompatibleKeyRequest request);

    /**
     * 获取用户的所有兼容API密钥
     *
     * @param userId 用户ID
     * @return 兼容密钥列表
     */
    List<CompatibleKeyDto> getUserCompatibleKeys(Long userId);

    /**
     * 根据ID获取兼容API密钥详情
     *
     * @param userId 用户ID
     * @param keyId 兼容密钥ID
     * @return 兼容密钥DTO
     */
    CompatibleKeyDto getCompatibleKeyById(Long userId, Long keyId);

    /**
     * 根据兼容密钥获取详情
     *
     * @param compatibleKey 兼容密钥
     * @return 兼容密钥实体
     */
    CompatibleApiKey getCompatibleKeyByKey(String compatibleKey);

    /**
     * 更新兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 兼容密钥ID
     * @param request 更新请求
     * @return 兼容密钥DTO
     */
    CompatibleKeyDto updateCompatibleKey(Long userId, Long keyId, CreateCompatibleKeyRequest request);

    /**
     * 删除兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 兼容密钥ID
     * @return 是否删除成功
     */
    boolean deleteCompatibleKey(Long userId, Long keyId);

    /**
     * 启用/禁用兼容API密钥
     *
     * @param userId 用户ID
     * @param keyId 兼容密钥ID
     * @param isActive 是否激活
     * @return 兼容密钥DTO
     */
    CompatibleKeyDto toggleCompatibleKey(Long userId, Long keyId, Boolean isActive);

    /**
     * 添加API密钥映射
     *
     * @param userId 用户ID
     * @param keyId 兼容密钥ID
     * @param userApiKeyId 普通API密钥ID
     * @param weight 权重
     * @return 兼容密钥DTO
     */
    CompatibleKeyDto addApiKeyMapping(Long userId, Long keyId, Long userApiKeyId, Integer weight);

    /**
     * 移除API密钥映射
     *
     * @param userId 用户ID
     * @param keyId 兼容密钥ID
     * @param mappingId 映射ID
     * @return 兼容密钥DTO
     */
    CompatibleKeyDto removeApiKeyMapping(Long userId, Long keyId, Long mappingId);

    /**
     * 验证兼容API密钥
     *
     * @param compatibleKey 兼容密钥
     * @return 验证结果
     */
    CompatibleKeyValidationResult validateCompatibleKey(String compatibleKey);

    /**
     * 更新兼容密钥使用统计
     *
     * @param compatibleKey 兼容密钥
     */
    void updateUsageStats(String compatibleKey);

    /**
     * 兼容密钥验证结果
     */
    class CompatibleKeyValidationResult {
        private final boolean valid;
        private final Long userId;
        private final CompatibleApiKey compatibleApiKey;
        private final String message;

        public CompatibleKeyValidationResult(boolean valid, Long userId, CompatibleApiKey compatibleApiKey, String message) {
            this.valid = valid;
            this.userId = userId;
            this.compatibleApiKey = compatibleApiKey;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public CompatibleApiKey getCompatibleApiKey() { return compatibleApiKey; }
        public String getMessage() { return message; }
    }
}
