package com.example.pure.service.openai;

import com.example.pure.model.entity.UserApiKey;

/**
 * 简单兼容密钥服务接口
 * <p>
 * 基于OpenAI格式的兼容密钥，通过解析密钥获取用户ID，
 * 然后使用现有的负载均衡服务选择最合适的原生API密钥
 * </p>
 */
public interface SimpleCompatibleKeyService {

    /**
     * 生成OpenAI格式的兼容API密钥
     *
     * @param userId 用户ID
     * @param keyName 密钥名称（用于标识）
     * @return 兼容格式的API密钥
     */
    String generateCompatibleApiKey(Long userId, String keyName);

    /**
     * 验证兼容API密钥并获取用户ID
     *
     * @param compatibleKey 兼容格式的API密钥
     * @return 验证结果
     */
    CompatibleKeyValidationResult validateCompatibleKey(String compatibleKey);

    /**
     * 根据兼容密钥和模型选择最佳的原生API密钥
     *
     * @param compatibleKey 兼容格式的API密钥
     * @param model 请求的模型名称
     * @return API密钥选择结果
     */
    ApiKeySelectionResult selectApiKeyForModel(String compatibleKey, String model);

    /**
     * 兼容密钥验证结果
     */
    class CompatibleKeyValidationResult {
        private final boolean valid;
        private final Long userId;
        private final String message;

        public CompatibleKeyValidationResult(boolean valid, Long userId, String message) {
            this.valid = valid;
            this.userId = userId;
            this.message = message;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public String getMessage() { return message; }
    }

    /**
     * API密钥选择结果
     */
    class ApiKeySelectionResult {
        private final boolean success;
        private final UserApiKey selectedApiKey;
        private final String decryptedKey;
        private final String message;

        public ApiKeySelectionResult(boolean success, UserApiKey selectedApiKey, String decryptedKey, String message) {
            this.success = success;
            this.selectedApiKey = selectedApiKey;
            this.decryptedKey = decryptedKey;
            this.message = message;
        }

        public boolean isSuccess() { return success; }
        public UserApiKey getSelectedApiKey() { return selectedApiKey; }
        public String getDecryptedKey() { return decryptedKey; }
        public String getMessage() { return message; }
    }
}
