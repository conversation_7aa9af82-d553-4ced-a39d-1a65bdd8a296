package com.example.pure.controller.openai;

import com.example.pure.common.Result;
import com.example.pure.config.AiConfig;
import com.example.pure.model.dto.response.openai.ApiKeyParseResult;
import com.example.pure.service.openai.AiConfigService;
import com.example.pure.service.openai.LoadBalancerService;
import com.example.pure.service.openai.ModelAdapterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * AI系统测试控制器
 * <p>
 * 提供系统健康检查和基础测试功能
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/test")
@RequiredArgsConstructor
@Tag(name = "AI系统测试", description = "AI系统健康检查和测试接口")
public class AiSystemTestController {

    private final AiConfig aiConfig;
    private final AiConfigService aiConfigService;
    private final LoadBalancerService loadBalancerService;
    private final ModelAdapterService modelAdapterService;

    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "系统健康检查", description = "检查AI系统各组件的健康状态")
    public Result<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();

            // 基础信息
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());

            // 配置信息
            Map<String, Object> configInfo = new HashMap<>();
            configInfo.put("defaultModel", aiConfig.getDefaults().getModel());
            configInfo.put("maxTimeout", aiConfig.getSecurity().getMaxTimeout());
            configInfo.put("maxTokensLimit", aiConfig.getSecurity().getMaxTokensLimit());
            configInfo.put("providersCount", aiConfig.getProviders() != null ? aiConfig.getProviders().size() : 0);
            health.put("config", configInfo);

            // 服务状态
            Map<String, Object> services = new HashMap<>();
            services.put("aiConfigService", "UP");
            services.put("loadBalancerService", "UP");
            services.put("modelAdapterService", "UP");
            health.put("services", services);

            // 支持的提供商
            if (aiConfig.getProviders() != null) {
                health.put("supportedProviders", aiConfig.getProviders().keySet());
            }

            log.debug("AI系统健康检查完成");
            return Result.success("系统健康", health);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            Map<String, Object> errorHealth = new HashMap<>();
            errorHealth.put("status", "DOWN");
            errorHealth.put("error", e.getMessage());
            errorHealth.put("timestamp", System.currentTimeMillis());
            return Result.error(500, "系统异常", errorHealth);
        }
    }

    /**
     * 获取系统配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取系统配置", description = "获取AI系统的配置信息")
    public Result<AiConfig> getSystemConfig() {
        try {
            log.debug("获取AI系统配置");
            return Result.success("获取成功", aiConfig);
        } catch (Exception e) {
            log.error("获取系统配置失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试模型支持情况
     */
    @GetMapping("/models/support/{model}")
    @Operation(summary = "测试模型支持", description = "检查指定模型是否被系统支持")
    public Result<Map<String, Object>> testModelSupport(@PathVariable String model) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("model", model);
            result.put("supported", aiConfig.isModelSupported(model));
            result.put("provider", aiConfig.getProviderByModel(model));

            log.debug("测试模型支持完成 - 模型: {}, 支持: {}", model, result.get("supported"));
            return Result.success("测试完成", result);
        } catch (Exception e) {
            log.error("测试模型支持失败", e);
            return Result.errorTyped(500, "测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取系统统计", description = "获取AI系统的运行统计信息")
    public Result<Map<String, Object>> getSystemStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // JVM信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvm = new HashMap<>();
            jvm.put("totalMemory", runtime.totalMemory());
            jvm.put("freeMemory", runtime.freeMemory());
            jvm.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            jvm.put("maxMemory", runtime.maxMemory());
            jvm.put("availableProcessors", runtime.availableProcessors());
            stats.put("jvm", jvm);

            // 系统信息
            Map<String, Object> system = new HashMap<>();
            system.put("osName", System.getProperty("os.name"));
            system.put("osVersion", System.getProperty("os.version"));
            system.put("javaVersion", System.getProperty("java.version"));
            stats.put("system", system);

            // 时间信息
            stats.put("timestamp", System.currentTimeMillis());
            stats.put("uptime", System.currentTimeMillis()); // 简化处理

            log.debug("获取系统统计信息完成");
            return Result.success("获取成功", stats);
        } catch (Exception e) {
            log.error("获取系统统计失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试API密钥解析
     */
    @PostMapping("/parse-key")
    @Operation(summary = "测试API密钥解析", description = "测试兼容格式API密钥的解析功能")
    public Result<Map<String, Object>> testParseApiKey(@RequestBody Map<String, String> request) {
        try {
            String compatibleKey = request.get("apiKey");
            if (compatibleKey == null || compatibleKey.trim().isEmpty()) {
                return Result.errorTyped(400, "API密钥不能为空");
            }

            // 使用新的兼容密钥服务验证
            CompatibleKeyService.CompatibleKeyValidationResult result =
                compatibleKeyService.validateCompatibleKey(compatibleKey);

            Map<String, Object> response = new HashMap<>();
            response.put("valid", result.isValid());
            response.put("userId", result.getUserId());
            response.put("keyId", result.getKeyId());
            response.put("hash", result.getHash());

            log.debug("测试API密钥解析完成 - 有效: {}", result.isValid());
            return Result.success("解析完成", response);
        } catch (Exception e) {
            log.error("测试API密钥解析失败", e);
            return Result.errorTyped(500, "解析失败: " + e.getMessage());
        }
    }
}
