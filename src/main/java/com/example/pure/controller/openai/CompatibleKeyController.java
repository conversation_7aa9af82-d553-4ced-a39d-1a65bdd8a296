package com.example.pure.controller.openai;

import com.example.pure.common.Result;
import com.example.pure.model.dto.request.openai.CreateCompatibleKeyRequest;
import com.example.pure.model.dto.response.openai.CompatibleKeyDto;
import com.example.pure.security.CustomUserDetails;
import com.example.pure.service.openai.CompatibleKeyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 兼容API密钥管理控制器
 * <p>
 * 提供兼容API密钥的管理功能，一个兼容密钥可以对应多个普通API密钥
 * </p>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/ai/compatible-keys")
@RequiredArgsConstructor
@Tag(name = "兼容API密钥管理", description = "兼容API密钥管理接口")
public class CompatibleKeyController {

    private final CompatibleKeyService compatibleKeyService;

    /**
     * 创建兼容API密钥
     */
    @PostMapping
    @Operation(summary = "创建兼容API密钥", description = "创建一个兼容密钥对应多个普通API密钥")
    @ApiResponse(responseCode = "200", description = "创建成功")
    public Result<CompatibleKeyDto> createCompatibleKey(
            @Valid @RequestBody CreateCompatibleKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleKeyDto dto = compatibleKeyService.createCompatibleKey(userId, request);

            log.info("创建兼容API密钥成功 - 用户ID: {}, 密钥名称: {}, 关联密钥数: {}",
                    userId, request.getKeyName(), request.getApiKeyMappings().size());
            return Result.success("创建成功", dto);
        } catch (Exception e) {
            log.error("创建兼容API密钥失败", e);
            return Result.errorTyped(500, "创建失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的所有兼容API密钥
     */
    @GetMapping
    @Operation(summary = "获取兼容API密钥列表", description = "获取当前用户的所有兼容API密钥")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<List<CompatibleKeyDto>> getUserCompatibleKeys(Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            List<CompatibleKeyDto> dtos = compatibleKeyService.getUserCompatibleKeys(userId);

            log.debug("获取用户兼容API密钥列表成功 - 用户ID: {}, 数量: {}", userId, dtos.size());
            return Result.success("获取成功", dtos);
        } catch (Exception e) {
            log.error("获取用户兼容API密钥列表失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取兼容API密钥详情
     */
    @GetMapping("/{keyId}")
    @Operation(summary = "获取兼容API密钥详情", description = "获取指定兼容API密钥的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    public Result<CompatibleKeyDto> getCompatibleKeyById(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleKeyDto dto = compatibleKeyService.getCompatibleKeyById(userId, keyId);

            if (dto != null) {
                log.debug("获取兼容API密钥详情成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("获取成功", dto);
            } else {
                return Result.errorTyped(404, "兼容API密钥不存在");
            }
        } catch (Exception e) {
            log.error("获取兼容API密钥详情失败", e);
            return Result.errorTyped(500, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 更新兼容API密钥
     */
    @PutMapping("/{keyId}")
    @Operation(summary = "更新兼容API密钥", description = "更新指定的兼容API密钥信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    public Result<CompatibleKeyDto> updateCompatibleKey(
            @PathVariable @NotNull Long keyId,
            @Valid @RequestBody CreateCompatibleKeyRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleKeyDto dto = compatibleKeyService.updateCompatibleKey(userId, keyId, request);

            log.info("更新兼容API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
            return Result.success("更新成功", dto);
        } catch (Exception e) {
            log.error("更新兼容API密钥失败", e);
            return Result.errorTyped(500, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除兼容API密钥
     */
    @DeleteMapping("/{keyId}")
    @Operation(summary = "删除兼容API密钥", description = "删除指定的兼容API密钥")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public Result<String> deleteCompatibleKey(
            @PathVariable @NotNull Long keyId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            boolean deleted = compatibleKeyService.deleteCompatibleKey(userId, keyId);

            if (deleted) {
                log.info("删除兼容API密钥成功 - 用户ID: {}, 密钥ID: {}", userId, keyId);
                return Result.success("删除成功", "删除成功");
            } else {
                return Result.errorTyped(404, "兼容API密钥不存在");
            }
        } catch (Exception e) {
            log.error("删除兼容API密钥失败", e);
            return Result.errorTyped(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用兼容API密钥
     */
    @PostMapping("/{keyId}/toggle")
    @Operation(summary = "启用/禁用兼容API密钥", description = "切换兼容API密钥的激活状态")
    @ApiResponse(responseCode = "200", description = "操作成功")
    public Result<CompatibleKeyDto> toggleCompatibleKey(
            @PathVariable @NotNull Long keyId,
            @RequestParam Boolean isActive,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleKeyDto dto = compatibleKeyService.toggleCompatibleKey(userId, keyId, isActive);

            log.info("切换兼容API密钥状态成功 - 用户ID: {}, 密钥ID: {}, 状态: {}",
                    userId, keyId, isActive);
            return Result.success("操作成功", dto);
        } catch (Exception e) {
            log.error("切换兼容API密钥状态失败", e);
            return Result.errorTyped(500, "操作失败: " + e.getMessage());
        }
    }

    /**
     * 添加API密钥映射
     */
    @PostMapping("/{keyId}/mappings")
    @Operation(summary = "添加API密钥映射", description = "为兼容密钥添加新的API密钥映射")
    @ApiResponse(responseCode = "200", description = "添加成功")
    public Result<CompatibleKeyDto> addApiKeyMapping(
            @PathVariable @NotNull Long keyId,
            @RequestParam @NotNull Long userApiKeyId,
            @RequestParam(defaultValue = "10") Integer weight,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleKeyDto dto = compatibleKeyService.addApiKeyMapping(userId, keyId, userApiKeyId, weight);

            log.info("添加API密钥映射成功 - 用户ID: {}, 兼容密钥ID: {}, API密钥ID: {}",
                    userId, keyId, userApiKeyId);
            return Result.success("添加成功", dto);
        } catch (Exception e) {
            log.error("添加API密钥映射失败", e);
            return Result.errorTyped(500, "添加失败: " + e.getMessage());
        }
    }

    /**
     * 移除API密钥映射
     */
    @DeleteMapping("/{keyId}/mappings/{mappingId}")
    @Operation(summary = "移除API密钥映射", description = "移除兼容密钥的API密钥映射")
    @ApiResponse(responseCode = "200", description = "移除成功")
    public Result<CompatibleKeyDto> removeApiKeyMapping(
            @PathVariable @NotNull Long keyId,
            @PathVariable @NotNull Long mappingId,
            Authentication authentication) {
        try {
            Long userId = getUserId(authentication);
            CompatibleKeyDto dto = compatibleKeyService.removeApiKeyMapping(userId, keyId, mappingId);

            log.info("移除API密钥映射成功 - 用户ID: {}, 兼容密钥ID: {}, 映射ID: {}",
                    userId, keyId, mappingId);
            return Result.success("移除成功", dto);
        } catch (Exception e) {
            log.error("移除API密钥映射失败", e);
            return Result.errorTyped(500, "移除失败: " + e.getMessage());
        }
    }

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null) {
            throw new RuntimeException("用户未认证");
        }

        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            return userDetails.getUserId();
        } catch (Exception e) {
            throw new RuntimeException("无效的用户ID");
        }
    }
}
