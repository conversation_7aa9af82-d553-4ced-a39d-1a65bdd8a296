package com.example.pure.mapper.primary;

import com.example.pure.model.entity.CompatibleKeyMapping;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 兼容密钥映射关系Mapper接口
 * <p>
 * 提供兼容密钥与普通API密钥映射关系的数据库操作方法
 * </p>
 */
@Mapper
public interface CompatibleKeyMappingMapper {

    /**
     * 插入映射关系
     *
     * @param mapping 映射关系实体
     * @return 影响行数
     */
    @Insert("INSERT INTO compatible_key_mappings (compatible_key_id, user_api_key_id, weight, " +
            "is_active, created_at, updated_at) " +
            "VALUES (#{compatibleKeyId}, #{userApiKeyId}, #{weight}, #{isActive}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CompatibleKeyMapping mapping);

    /**
     * 批量插入映射关系
     *
     * @param mappings 映射关系列表
     * @return 影响行数
     */
    @Insert("<script>" +
            "INSERT INTO compatible_key_mappings (compatible_key_id, user_api_key_id, weight, " +
            "is_active, created_at, updated_at) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.compatibleKeyId}, #{item.userApiKeyId}, #{item.weight}, #{item.isActive}, " +
            "#{item.createdAt}, #{item.updatedAt})" +
            "</foreach>" +
            "</script>")
    int batchInsert(List<CompatibleKeyMapping> mappings);

    /**
     * 根据ID查询映射关系
     *
     * @param id 映射关系ID
     * @return 映射关系实体
     */
    @Select("SELECT * FROM compatible_key_mappings WHERE id = #{id}")
    CompatibleKeyMapping selectById(Long id);

    /**
     * 根据兼容密钥ID查询映射关系列表
     *
     * @param compatibleKeyId 兼容密钥ID
     * @return 映射关系列表
     */
    @Select("SELECT * FROM compatible_key_mappings WHERE compatible_key_id = #{compatibleKeyId} " +
            "ORDER BY weight DESC, created_at ASC")
    List<CompatibleKeyMapping> selectByCompatibleKeyId(Long compatibleKeyId);

    /**
     * 根据兼容密钥ID查询激活的映射关系列表
     *
     * @param compatibleKeyId 兼容密钥ID
     * @return 激活的映射关系列表
     */
    @Select("SELECT * FROM compatible_key_mappings WHERE compatible_key_id = #{compatibleKeyId} " +
            "AND is_active = true ORDER BY weight DESC, created_at ASC")
    List<CompatibleKeyMapping> selectActiveByCompatibleKeyId(Long compatibleKeyId);

    /**
     * 根据普通API密钥ID查询映射关系列表
     *
     * @param userApiKeyId 普通API密钥ID
     * @return 映射关系列表
     */
    @Select("SELECT * FROM compatible_key_mappings WHERE user_api_key_id = #{userApiKeyId}")
    List<CompatibleKeyMapping> selectByUserApiKeyId(Long userApiKeyId);

    /**
     * 查询兼容密钥关联的普通API密钥详情
     *
     * @param compatibleKeyId 兼容密钥ID
     * @return 关联的API密钥信息
     */
    @Select("SELECT m.id as mapping_id, m.weight, m.is_active as mapping_active, " +
            "u.id, u.user_id, u.provider, u.key_name, u.api_key_encrypted, u.is_active, " +
            "u.priority, u.usage_count, u.last_used_at, u.created_at " +
            "FROM compatible_key_mappings m " +
            "JOIN user_api_keys u ON m.user_api_key_id = u.id " +
            "WHERE m.compatible_key_id = #{compatibleKeyId} " +
            "ORDER BY m.weight DESC, m.created_at ASC")
    @Results({
        @Result(property = "mappingId", column = "mapping_id"),
        @Result(property = "weight", column = "weight"),
        @Result(property = "mappingActive", column = "mapping_active"),
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "provider", column = "provider"),
        @Result(property = "keyName", column = "key_name"),
        @Result(property = "apiKeyEncrypted", column = "api_key_encrypted"),
        @Result(property = "isActive", column = "is_active"),
        @Result(property = "priority", column = "priority"),
        @Result(property = "usageCount", column = "usage_count"),
        @Result(property = "lastUsedAt", column = "last_used_at"),
        @Result(property = "createdAt", column = "created_at")
    })
    List<MappedApiKeyInfo> selectMappedApiKeys(Long compatibleKeyId);

    /**
     * 更新映射关系
     *
     * @param mapping 映射关系实体
     * @return 影响行数
     */
    @Update("UPDATE compatible_key_mappings SET weight = #{weight}, is_active = #{isActive}, " +
            "updated_at = #{updatedAt} WHERE id = #{id}")
    int updateById(CompatibleKeyMapping mapping);

    /**
     * 删除映射关系
     *
     * @param id 映射关系ID
     * @return 影响行数
     */
    @Delete("DELETE FROM compatible_key_mappings WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 根据兼容密钥ID删除所有映射关系
     *
     * @param compatibleKeyId 兼容密钥ID
     * @return 影响行数
     */
    @Delete("DELETE FROM compatible_key_mappings WHERE compatible_key_id = #{compatibleKeyId}")
    int deleteByCompatibleKeyId(Long compatibleKeyId);

    /**
     * 根据普通API密钥ID删除映射关系
     *
     * @param userApiKeyId 普通API密钥ID
     * @return 影响行数
     */
    @Delete("DELETE FROM compatible_key_mappings WHERE user_api_key_id = #{userApiKeyId}")
    int deleteByUserApiKeyId(Long userApiKeyId);

    /**
     * 检查映射关系是否存在
     *
     * @param compatibleKeyId 兼容密钥ID
     * @param userApiKeyId 普通API密钥ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM compatible_key_mappings " +
            "WHERE compatible_key_id = #{compatibleKeyId} AND user_api_key_id = #{userApiKeyId}")
    boolean existsByCompatibleKeyIdAndUserApiKeyId(@Param("compatibleKeyId") Long compatibleKeyId, 
                                                  @Param("userApiKeyId") Long userApiKeyId);

    /**
     * 统计兼容密钥的映射数量
     *
     * @param compatibleKeyId 兼容密钥ID
     * @return 映射数量
     */
    @Select("SELECT COUNT(*) FROM compatible_key_mappings WHERE compatible_key_id = #{compatibleKeyId}")
    int countByCompatibleKeyId(Long compatibleKeyId);

    /**
     * 统计兼容密钥的激活映射数量
     *
     * @param compatibleKeyId 兼容密钥ID
     * @return 激活映射数量
     */
    @Select("SELECT COUNT(*) FROM compatible_key_mappings " +
            "WHERE compatible_key_id = #{compatibleKeyId} AND is_active = true")
    int countActiveByCompatibleKeyId(Long compatibleKeyId);

    /**
     * 映射的API密钥信息
     */
    class MappedApiKeyInfo {
        private Long mappingId;
        private Integer weight;
        private Boolean mappingActive;
        private Long id;
        private Long userId;
        private String provider;
        private String keyName;
        private String apiKeyEncrypted;
        private Boolean isActive;
        private Integer priority;
        private Long usageCount;
        private java.time.Instant lastUsedAt;
        private java.time.Instant createdAt;

        // Getters and Setters
        public Long getMappingId() { return mappingId; }
        public void setMappingId(Long mappingId) { this.mappingId = mappingId; }
        public Integer getWeight() { return weight; }
        public void setWeight(Integer weight) { this.weight = weight; }
        public Boolean getMappingActive() { return mappingActive; }
        public void setMappingActive(Boolean mappingActive) { this.mappingActive = mappingActive; }
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        public String getKeyName() { return keyName; }
        public void setKeyName(String keyName) { this.keyName = keyName; }
        public String getApiKeyEncrypted() { return apiKeyEncrypted; }
        public void setApiKeyEncrypted(String apiKeyEncrypted) { this.apiKeyEncrypted = apiKeyEncrypted; }
        public Boolean getIsActive() { return isActive; }
        public void setIsActive(Boolean isActive) { this.isActive = isActive; }
        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
        public Long getUsageCount() { return usageCount; }
        public void setUsageCount(Long usageCount) { this.usageCount = usageCount; }
        public java.time.Instant getLastUsedAt() { return lastUsedAt; }
        public void setLastUsedAt(java.time.Instant lastUsedAt) { this.lastUsedAt = lastUsedAt; }
        public java.time.Instant getCreatedAt() { return createdAt; }
        public void setCreatedAt(java.time.Instant createdAt) { this.createdAt = createdAt; }
    }
}
