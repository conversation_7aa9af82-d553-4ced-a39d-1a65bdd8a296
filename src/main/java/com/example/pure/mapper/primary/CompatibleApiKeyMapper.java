package com.example.pure.mapper.primary;

import com.example.pure.model.entity.CompatibleApiKey;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 兼容API密钥Mapper接口
 * <p>
 * 提供兼容API密钥的数据库操作方法
 * </p>
 */
@Mapper
public interface CompatibleApiKeyMapper {

    /**
     * 插入兼容API密钥
     *
     * @param compatibleApiKey 兼容API密钥实体
     * @return 影响行数
     */
    @Insert("INSERT INTO compatible_api_keys (user_id, snowflake_id, key_name, compatible_key, " +
            "security_hash, is_active, description, usage_count, created_at, updated_at, expires_at) " +
            "VALUES (#{userId}, #{snowflakeId}, #{keyName}, #{compatibleKey}, #{securityHash}, " +
            "#{isActive}, #{description}, #{usageCount}, #{createdAt}, #{updatedAt}, #{expiresAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(CompatibleApiKey compatibleApiKey);

    /**
     * 根据ID查询兼容API密钥
     *
     * @param id 兼容API密钥ID
     * @return 兼容API密钥实体
     */
    @Select("SELECT * FROM compatible_api_keys WHERE id = #{id}")
    CompatibleApiKey selectById(Long id);

    /**
     * 根据兼容密钥查询
     *
     * @param compatibleKey 兼容密钥
     * @return 兼容API密钥实体
     */
    @Select("SELECT * FROM compatible_api_keys WHERE compatible_key = #{compatibleKey}")
    CompatibleApiKey selectByCompatibleKey(String compatibleKey);

    /**
     * 根据雪花ID查询
     *
     * @param snowflakeId 雪花ID
     * @return 兼容API密钥实体
     */
    @Select("SELECT * FROM compatible_api_keys WHERE snowflake_id = #{snowflakeId}")
    CompatibleApiKey selectBySnowflakeId(Long snowflakeId);

    /**
     * 根据用户ID查询兼容API密钥列表
     *
     * @param userId 用户ID
     * @return 兼容API密钥列表
     */
    @Select("SELECT * FROM compatible_api_keys WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<CompatibleApiKey> selectByUserId(Long userId);

    /**
     * 根据用户ID查询激活的兼容API密钥列表
     *
     * @param userId 用户ID
     * @return 激活的兼容API密钥列表
     */
    @Select("SELECT * FROM compatible_api_keys WHERE user_id = #{userId} AND is_active = true " +
            "AND (expires_at IS NULL OR expires_at > NOW()) ORDER BY created_at DESC")
    List<CompatibleApiKey> selectActiveByUserId(Long userId);

    /**
     * 更新兼容API密钥
     *
     * @param compatibleApiKey 兼容API密钥实体
     * @return 影响行数
     */
    @Update("UPDATE compatible_api_keys SET key_name = #{keyName}, is_active = #{isActive}, " +
            "description = #{description}, updated_at = #{updatedAt}, expires_at = #{expiresAt} " +
            "WHERE id = #{id}")
    int updateById(CompatibleApiKey compatibleApiKey);

    /**
     * 更新使用统计
     *
     * @param id 兼容API密钥ID
     * @param usageCount 使用次数
     * @param lastUsedAt 最后使用时间
     * @return 影响行数
     */
    @Update("UPDATE compatible_api_keys SET usage_count = #{usageCount}, last_used_at = #{lastUsedAt}, " +
            "updated_at = NOW() WHERE id = #{id}")
    int updateUsageStats(@Param("id") Long id, @Param("usageCount") Long usageCount, 
                        @Param("lastUsedAt") java.time.Instant lastUsedAt);

    /**
     * 删除兼容API密钥
     *
     * @param id 兼容API密钥ID
     * @return 影响行数
     */
    @Delete("DELETE FROM compatible_api_keys WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 根据用户ID删除兼容API密钥
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    @Delete("DELETE FROM compatible_api_keys WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);

    /**
     * 统计用户的兼容API密钥数量
     *
     * @param userId 用户ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM compatible_api_keys WHERE user_id = #{userId}")
    int countByUserId(Long userId);

    /**
     * 统计用户的激活兼容API密钥数量
     *
     * @param userId 用户ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM compatible_api_keys WHERE user_id = #{userId} AND is_active = true " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    int countActiveByUserId(Long userId);

    /**
     * 检查兼容密钥是否存在
     *
     * @param compatibleKey 兼容密钥
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM compatible_api_keys WHERE compatible_key = #{compatibleKey}")
    boolean existsByCompatibleKey(String compatibleKey);

    /**
     * 检查用户是否拥有指定的兼容密钥
     *
     * @param userId 用户ID
     * @param id 兼容API密钥ID
     * @return 是否拥有
     */
    @Select("SELECT COUNT(*) > 0 FROM compatible_api_keys WHERE user_id = #{userId} AND id = #{id}")
    boolean existsByUserIdAndId(@Param("userId") Long userId, @Param("id") Long id);
}
