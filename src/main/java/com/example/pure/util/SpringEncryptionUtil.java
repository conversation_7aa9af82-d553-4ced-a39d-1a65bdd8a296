package com.example.pure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * Spring加密工具类
 * <p>
 * 使用Spring Security的加密器进行API密钥加密和解密
 * </p>
 */
@Slf4j
@Component
public class SpringEncryptionUtil {

    @Value("${app.encryption.password:defaultPassword123}")
    private String encryptionPassword;

    @Value("${app.encryption.salt:defaultSalt456}")
    private String encryptionSalt;

    private TextEncryptor textEncryptor;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    public SpringEncryptionUtil(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    @PostConstruct
    public void init() {
        try {
            // 使用更强的加密算法
            this.textEncryptor = Encryptors.text(encryptionPassword, encryptionSalt);
            log.info("Spring加密工具初始化成功");
        } catch (Exception e) {
            log.error("Spring加密工具初始化失败", e);
            throw new RuntimeException("加密工具初始化失败", e);
        }
    }

    /**
     * 加密API密钥
     *
     * @param apiKey 原始API密钥
     * @return 加密后的API密钥
     */
    public String encrypt(String apiKey) {
        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("API密钥不能为空");
            }
            return textEncryptor.encrypt(apiKey);
        } catch (Exception e) {
            log.error("加密API密钥失败", e);
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密API密钥
     *
     * @param encryptedApiKey 加密后的API密钥
     * @return 原始API密钥
     */
    public String decrypt(String encryptedApiKey) {
        try {
            if (encryptedApiKey == null || encryptedApiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("加密的API密钥不能为空");
            }
            return textEncryptor.decrypt(encryptedApiKey);
        } catch (Exception e) {
            log.error("解密API密钥失败", e);
            throw new RuntimeException("解密失败", e);
        }
    }

    /**
     * 生成兼容格式的API密钥
     * 格式：sk-comp-{snowflakeId}-{hash}
     *
     * @param userId 用户ID
     * @param keyName 密钥名称
     * @return 兼容格式的API密钥
     */
    public CompatibleKeyResult generateCompatibleApiKey(Long userId, String keyName) {
        try {
            // 生成雪花ID
            long snowflakeId = snowflakeIdGenerator.nextId();
            
            // 生成安全哈希
            String data = userId + "_" + keyName + "_" + snowflakeId + "_" + System.currentTimeMillis();
            String securityHash = generateHash(data);
            String shortHash = securityHash.substring(0, 6); // 取前6位
            
            // 组装兼容密钥
            String compatibleKey = String.format("sk-comp-%d-%s", snowflakeId, shortHash);
            
            return new CompatibleKeyResult(snowflakeId, compatibleKey, securityHash);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败", e);
            throw new RuntimeException("生成兼容API密钥失败", e);
        }
    }

    /**
     * 解析兼容格式的API密钥
     * 从格式 sk-comp-{snowflakeId}-{hash} 中提取信息
     *
     * @param compatibleKey 兼容格式的API密钥
     * @return 解析结果
     */
    public ParseResult parseCompatibleApiKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-comp-")) {
                return new ParseResult(false, null, null);
            }
            
            String[] parts = compatibleKey.substring(8).split("-"); // 去掉 "sk-comp-"
            if (parts.length != 2) {
                return new ParseResult(false, null, null);
            }
            
            Long snowflakeId = Long.parseLong(parts[0]);
            String hash = parts[1];
            
            return new ParseResult(true, snowflakeId, hash);
        } catch (Exception e) {
            log.warn("解析兼容API密钥失败: {}", compatibleKey, e);
            return new ParseResult(false, null, null);
        }
    }

    /**
     * 验证兼容API密钥的哈希值
     *
     * @param compatibleKey 兼容格式的API密钥
     * @param expectedHash 期望的完整哈希值
     * @return 是否有效
     */
    public boolean validateCompatibleApiKey(String compatibleKey, String expectedHash) {
        ParseResult result = parseCompatibleApiKey(compatibleKey);
        if (!result.isValid()) {
            return false;
        }
        
        // 比较哈希值的前6位
        return expectedHash != null && expectedHash.startsWith(result.getHash());
    }

    /**
     * 生成哈希值
     */
    private String generateHash(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
    }

    /**
     * 脱敏显示API密钥
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        
        if (apiKey.startsWith("sk-")) {
            // OpenAI格式密钥脱敏
            return apiKey.substring(0, 7) + "****" + apiKey.substring(apiKey.length() - 4);
        } else {
            // 其他格式密钥脱敏
            return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
        }
    }

    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final Long snowflakeId;
        private final String compatibleKey;
        private final String securityHash;

        public CompatibleKeyResult(Long snowflakeId, String compatibleKey, String securityHash) {
            this.snowflakeId = snowflakeId;
            this.compatibleKey = compatibleKey;
            this.securityHash = securityHash;
        }

        public Long getSnowflakeId() { return snowflakeId; }
        public String getCompatibleKey() { return compatibleKey; }
        public String getSecurityHash() { return securityHash; }
    }

    /**
     * 解析结果
     */
    public static class ParseResult {
        private final boolean valid;
        private final Long snowflakeId;
        private final String hash;

        public ParseResult(boolean valid, Long snowflakeId, String hash) {
            this.valid = valid;
            this.snowflakeId = snowflakeId;
            this.hash = hash;
        }

        public boolean isValid() { return valid; }
        public Long getSnowflakeId() { return snowflakeId; }
        public String getHash() { return hash; }
    }
}
