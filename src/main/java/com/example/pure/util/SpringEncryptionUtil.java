package com.example.pure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.security.crypto.keygen.KeyGenerators;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

/**
 * Spring加密工具类
 * <p>
 * 使用Spring Security的加密器进行API密钥加密和解密
 * </p>
 */
@Slf4j
@Component
public class SpringEncryptionUtil {

    @Value("${app.encryption.password:defaultPassword123}")
    private String encryptionPassword;

    @Value("${app.encryption.salt:defaultSalt456}")
    private String encryptionSalt;

    private TextEncryptor textEncryptor;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    public SpringEncryptionUtil(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    @PostConstruct
    public void init() {
        try {
            // 使用更强的加密算法
            this.textEncryptor = Encryptors.text(encryptionPassword, encryptionSalt);
            log.info("Spring加密工具初始化成功");
        } catch (Exception e) {
            log.error("Spring加密工具初始化失败", e);
            throw new RuntimeException("加密工具初始化失败", e);
        }
    }

    /**
     * 加密API密钥
     *
     * @param apiKey 原始API密钥
     * @return 加密后的API密钥
     */
    public String encrypt(String apiKey) {
        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("API密钥不能为空");
            }
            return textEncryptor.encrypt(apiKey);
        } catch (Exception e) {
            log.error("加密API密钥失败", e);
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密API密钥
     *
     * @param encryptedApiKey 加密后的API密钥
     * @return 原始API密钥
     */
    public String decrypt(String encryptedApiKey) {
        try {
            if (encryptedApiKey == null || encryptedApiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("加密的API密钥不能为空");
            }
            return textEncryptor.decrypt(encryptedApiKey);
        } catch (Exception e) {
            log.error("解密API密钥失败", e);
            throw new RuntimeException("解密失败", e);
        }
    }

    /**
     * 生成OpenAI格式的兼容API密钥
     * 格式：sk-{encodedUserId}_{timestamp}_{hash}
     *
     * @param userId 用户ID
     * @param keyName 密钥名称（用于生成哈希）
     * @return 兼容格式的API密钥
     */
    public CompatibleKeyResult generateCompatibleApiKey(Long userId, String keyName) {
        try {
            // 编码用户ID（简单可逆编码）
            String encodedUserId = encodeUserId(userId);

            // 生成时间戳
            long timestamp = System.currentTimeMillis();

            // 生成安全哈希
            String data = userId + "_" + keyName + "_" + timestamp;
            String securityHash = generateHash(data);
            String shortHash = securityHash.substring(0, 8); // 取前8位

            // 组装OpenAI格式的兼容密钥
            String compatibleKey = String.format("sk-%s_%d_%s", encodedUserId, timestamp, shortHash);

            // 使用雪花ID作为内部标识
            long snowflakeId = snowflakeIdGenerator.nextId();

            return new CompatibleKeyResult(snowflakeId, compatibleKey, securityHash);
        } catch (Exception e) {
            log.error("生成兼容API密钥失败", e);
            throw new RuntimeException("生成兼容API密钥失败", e);
        }
    }

    /**
     * 编码用户ID（可逆的简单编码）
     * 使用简单的数学变换 + Base36编码
     */
    private String encodeUserId(Long userId) {
        try {
            // 简单的数学变换（可逆）
            long transformed = (userId * 7919 + 12345) ^ 0xABCDEF; // 使用质数和异或

            // 转换为Base36字符串（包含数字和字母）
            String encoded = Long.toString(Math.abs(transformed), 36).toUpperCase();

            // 确保长度一致，不足补0
            return String.format("%12s", encoded).replace(' ', '0');
        } catch (Exception e) {
            log.error("编码用户ID失败", e);
            throw new RuntimeException("编码用户ID失败", e);
        }
    }

    /**
     * 解码用户ID（从编码字符串恢复原始用户ID）
     */
    public Long decodeUserId(String encodedUserId) {
        try {
            // 从Base36字符串转换回数字
            long transformed = Long.parseLong(encodedUserId.trim(), 36);

            // 反向数学变换
            long userId = ((transformed ^ 0xABCDEF) - 12345) / 7919;

            return userId;
        } catch (Exception e) {
            log.error("解码用户ID失败: {}", encodedUserId, e);
            throw new RuntimeException("解码用户ID失败", e);
        }
    }

    /**
     * 解析OpenAI格式的兼容API密钥
     * 从格式 sk-{encodedUserId}_{timestamp}_{hash} 中提取信息
     *
     * @param compatibleKey 兼容格式的API密钥
     * @return 解析结果
     */
    public ParseResult parseCompatibleApiKey(String compatibleKey) {
        try {
            if (compatibleKey == null || !compatibleKey.startsWith("sk-")) {
                return new ParseResult(false, null, null);
            }

            String[] parts = compatibleKey.substring(3).split("_"); // 去掉 "sk-"
            if (parts.length != 3) {
                return new ParseResult(false, null, null);
            }

            String encodedUserId = parts[0];
            Long timestamp = Long.parseLong(parts[1]);
            String hash = parts[2];

            // 解码用户ID
            Long userId = decodeUserId(encodedUserId);

            return new ParseResult(true, userId, timestamp, hash);
        } catch (Exception e) {
            log.warn("解析兼容API密钥失败: {}", compatibleKey, e);
            return new ParseResult(false, null, null, null);
        }
    }

    /**
     * 验证兼容API密钥的哈希值
     *
     * @param compatibleKey 兼容格式的API密钥
     * @param expectedHash 期望的完整哈希值
     * @return 是否有效
     */
    public boolean validateCompatibleApiKey(String compatibleKey, String expectedHash) {
        ParseResult result = parseCompatibleApiKey(compatibleKey);
        if (!result.isValid()) {
            return false;
        }
        
        // 比较哈希值的前6位
        return expectedHash != null && expectedHash.startsWith(result.getHash());
    }

    /**
     * 生成哈希值
     */
    private String generateHash(String data) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hashBytes).replaceAll("[^a-zA-Z0-9]", "");
    }

    /**
     * 脱敏显示API密钥
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        
        if (apiKey.startsWith("sk-")) {
            // OpenAI格式密钥脱敏
            return apiKey.substring(0, 7) + "****" + apiKey.substring(apiKey.length() - 4);
        } else {
            // 其他格式密钥脱敏
            return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
        }
    }

    /**
     * 兼容密钥生成结果
     */
    public static class CompatibleKeyResult {
        private final Long snowflakeId;
        private final String compatibleKey;
        private final String securityHash;

        public CompatibleKeyResult(Long snowflakeId, String compatibleKey, String securityHash) {
            this.snowflakeId = snowflakeId;
            this.compatibleKey = compatibleKey;
            this.securityHash = securityHash;
        }

        public Long getSnowflakeId() { return snowflakeId; }
        public String getCompatibleKey() { return compatibleKey; }
        public String getSecurityHash() { return securityHash; }
    }

    /**
     * 解析结果
     */
    public static class ParseResult {
        private final boolean valid;
        private final Long userId;
        private final Long timestamp;
        private final String hash;

        public ParseResult(boolean valid, Long userId, Long timestamp, String hash) {
            this.valid = valid;
            this.userId = userId;
            this.timestamp = timestamp;
            this.hash = hash;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getTimestamp() { return timestamp; }
        public String getHash() { return hash; }
    }
}
