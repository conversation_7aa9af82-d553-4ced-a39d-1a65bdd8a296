package com.example.pure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * API密钥加密工具类（已废弃）
 * <p>
 * 此类已被SpringEncryptionUtil替代，保留用于向后兼容
 * </p>
 * @deprecated 请使用 {@link SpringEncryptionUtil} 替代
 */
@Slf4j
@Component
@Deprecated
public class ApiKeyEncryptionUtil {

    private final SpringEncryptionUtil springEncryptionUtil;

    @Autowired
    public ApiKeyEncryptionUtil(SpringEncryptionUtil springEncryptionUtil) {
        this.springEncryptionUtil = springEncryptionUtil;
        log.warn("ApiKeyEncryptionUtil已废弃，请使用SpringEncryptionUtil");
    }

    /**
     * 加密API密钥
     * @deprecated 请使用 {@link SpringEncryptionUtil#encrypt(String)}
     */
    @Deprecated
    public String encrypt(String apiKey) {
        return springEncryptionUtil.encrypt(apiKey);
    }

    /**
     * 解密API密钥
     * @deprecated 请使用 {@link SpringEncryptionUtil#decrypt(String)}
     */
    @Deprecated
    public String decrypt(String encryptedApiKey) {
        return springEncryptionUtil.decrypt(encryptedApiKey);
    }

    /**
     * 脱敏显示API密钥
     * @deprecated 请使用 {@link SpringEncryptionUtil#maskApiKey(String)}
     */
    @Deprecated
    public String maskApiKey(String apiKey) {
        return springEncryptionUtil.maskApiKey(apiKey);
    }

    /**
     * 生成兼容API密钥（已废弃）
     * @deprecated 兼容密钥功能已重构，请使用CompatibleKeyService
     */
    @Deprecated
    public String generateCompatibleApiKey(Long userId, Long keyId) {
        throw new UnsupportedOperationException("此方法已废弃，请使用CompatibleKeyService创建兼容密钥");
    }

    /**
     * 解析兼容API密钥（已废弃）
     * @deprecated 兼容密钥功能已重构，请使用CompatibleKeyService
     */
    @Deprecated
    public ParseResult parseCompatibleApiKey(String compatibleKey) {
        throw new UnsupportedOperationException("此方法已废弃，请使用CompatibleKeyService解析兼容密钥");
    }

    /**
     * 验证兼容API密钥（已废弃）
     * @deprecated 兼容密钥功能已重构，请使用CompatibleKeyService
     */
    @Deprecated
    public boolean validateCompatibleApiKey(String compatibleKey, Long userId, Long keyId) {
        throw new UnsupportedOperationException("此方法已废弃，请使用CompatibleKeyService验证兼容密钥");
    }

    /**
     * 解析结果（已废弃）
     * @deprecated 请使用新的兼容密钥解析结果类
     */
    @Deprecated
    public static class ParseResult {
        private final boolean valid;
        private final Long userId;
        private final Long keyId;
        private final String hash;

        public ParseResult(boolean valid, Long userId, Long keyId, String hash) {
            this.valid = valid;
            this.userId = userId;
            this.keyId = keyId;
            this.hash = hash;
        }

        public boolean isValid() { return valid; }
        public Long getUserId() { return userId; }
        public Long getKeyId() { return keyId; }
        public String getHash() { return hash; }
    }
}
