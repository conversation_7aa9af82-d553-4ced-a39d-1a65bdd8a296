package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.Instant;

/**
 * 兼容密钥映射关系实体类
 * <p>
 * 存储兼容API密钥与普通API密钥的映射关系
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CompatibleKeyMapping {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 兼容API密钥ID
     */
    private Long compatibleKeyId;

    /**
     * 普通API密钥ID
     */
    private Long userApiKeyId;

    /**
     * 权重（用于负载均衡，数字越大权重越高）
     */
    private Integer weight;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;
}
