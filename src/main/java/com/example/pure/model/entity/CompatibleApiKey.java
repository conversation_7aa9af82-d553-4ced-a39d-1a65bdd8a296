package com.example.pure.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.Instant;

/**
 * 兼容API密钥实体类
 * <p>
 * 存储兼容格式的API密钥信息，一个兼容密钥可以对应多个普通API密钥
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CompatibleApiKey {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 雪花算法生成的唯一ID
     */
    private Long snowflakeId;

    /**
     * 兼容密钥名称（用户自定义）
     */
    private String keyName;

    /**
     * 兼容格式的API密钥
     * 格式：sk-comp-{snowflakeId}-{hash}
     */
    private String compatibleKey;

    /**
     * 安全哈希值（用于验证）
     */
    private String securityHash;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 使用次数统计
     */
    private Long usageCount;

    /**
     * 最后使用时间
     */
    private Instant lastUsedAt;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 更新时间
     */
    private Instant updatedAt;

    /**
     * 过期时间（可选）
     */
    private Instant expiresAt;
}
