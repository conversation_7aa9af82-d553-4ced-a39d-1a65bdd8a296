package com.example.pure.model.dto.response.openai;

import com.example.pure.model.entity.UserApiKey;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.time.Instant;

/**
 * API密钥DTO
 * <p>
 * 用于API密钥的传输和展示（已脱敏）
 * </p>
 */
@Data
@Schema(description = "API密钥信息")
public class ApiKeyDto {

    /**
     * 密钥ID
     */
    @Schema(description = "密钥ID", example = "1")
    private Long id;

    /**
     * 提供商类型
     */
    @NotNull(message = "提供商类型不能为空")
    @Schema(description = "提供商类型", example = "OPENAI")
    private UserApiKey.ProviderType provider;

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不能为空")
    @Schema(description = "密钥名称", example = "主要密钥")
    private String keyName;

    /**
     * 脱敏后的API密钥（仅显示前缀和后缀）
     */
    @Schema(description = "脱敏后的API密钥", example = "sk-****...****abcd")
    private String maskedApiKey;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    /**
     * 优先级
     */
    @Min(value = 1, message = "优先级不能小于1")
    @Max(value = 100, message = "优先级不能大于100")
    @Schema(description = "优先级（数字越小优先级越高）", example = "1")
    private Integer priority;

    /**
     * 使用次数统计
     */
    @Schema(description = "使用次数统计", example = "1250")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间")
    private Instant lastUsedAt;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Instant createdAt;

    // 兼容格式的API密钥功能已迁移到CompatibleKeyService

    /**
     * 健康状态
     */
    @Schema(description = "健康状态", example = "true")
    private Boolean isHealthy;

    /**
     * 当前并发请求数
     */
    @Schema(description = "当前并发请求数", example = "0")
    private Integer currentRequests;

    /**
     * 错误次数
     */
    @Schema(description = "错误次数", example = "0")
    private Integer errorCount;

    /**
     * 错误率
     */
    @Schema(description = "错误率", example = "0.01")
    private Double errorRate;
}
