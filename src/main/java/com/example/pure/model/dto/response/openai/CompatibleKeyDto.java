package com.example.pure.model.dto.response.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 兼容API密钥DTO
 * <p>
 * 用于兼容API密钥的传输和展示
 * </p>
 */
@Data
@Schema(description = "兼容API密钥信息")
public class CompatibleKeyDto {

    /**
     * 兼容密钥ID
     */
    @Schema(description = "兼容密钥ID", example = "1")
    private Long id;

    /**
     * 雪花算法生成的唯一ID
     */
    @Schema(description = "雪花算法生成的唯一ID", example = "1234567890123456789")
    private Long snowflakeId;

    /**
     * 兼容密钥名称
     */
    @Schema(description = "兼容密钥名称", example = "主要兼容密钥")
    private String keyName;

    /**
     * 兼容格式的API密钥
     */
    @Schema(description = "兼容格式的API密钥", example = "sk-comp-1234567890123456789-a1b2c3")
    private String compatibleKey;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    /**
     * 描述信息
     */
    @Schema(description = "描述信息", example = "用于生产环境的兼容密钥")
    private String description;

    /**
     * 使用次数统计
     */
    @Schema(description = "使用次数统计", example = "1250")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间")
    private Instant lastUsedAt;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Instant createdAt;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private Instant expiresAt;

    /**
     * 关联的API密钥列表
     */
    @Schema(description = "关联的API密钥列表")
    private List<MappedApiKeyDto> mappedApiKeys;

    /**
     * 健康状态统计
     */
    @Schema(description = "健康状态统计")
    private HealthStats healthStats;

    /**
     * 映射的API密钥信息
     */
    @Data
    @Schema(description = "映射的API密钥信息")
    public static class MappedApiKeyDto {
        
        /**
         * 映射ID
         */
        @Schema(description = "映射ID", example = "1")
        private Long mappingId;

        /**
         * API密钥ID
         */
        @Schema(description = "API密钥ID", example = "1")
        private Long apiKeyId;

        /**
         * API密钥名称
         */
        @Schema(description = "API密钥名称", example = "OpenAI主密钥")
        private String keyName;

        /**
         * 提供商类型
         */
        @Schema(description = "提供商类型", example = "OPENAI")
        private String provider;

        /**
         * 脱敏后的API密钥
         */
        @Schema(description = "脱敏后的API密钥", example = "sk-****...****abcd")
        private String maskedApiKey;

        /**
         * 权重
         */
        @Schema(description = "权重", example = "10")
        private Integer weight;

        /**
         * 是否激活
         */
        @Schema(description = "是否激活", example = "true")
        private Boolean isActive;

        /**
         * 健康状态
         */
        @Schema(description = "健康状态", example = "true")
        private Boolean isHealthy;
    }

    /**
     * 健康状态统计
     */
    @Data
    @Schema(description = "健康状态统计")
    public static class HealthStats {
        
        /**
         * 总密钥数量
         */
        @Schema(description = "总密钥数量", example = "5")
        private Integer totalKeys;

        /**
         * 健康密钥数量
         */
        @Schema(description = "健康密钥数量", example = "4")
        private Integer healthyKeys;

        /**
         * 不健康密钥数量
         */
        @Schema(description = "不健康密钥数量", example = "1")
        private Integer unhealthyKeys;

        /**
         * 总权重
         */
        @Schema(description = "总权重", example = "50")
        private Integer totalWeight;

        /**
         * 可用权重
         */
        @Schema(description = "可用权重", example = "40")
        private Integer availableWeight;
    }
}
