package com.example.pure.model.dto.request.openai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.Instant;
import java.util.List;

/**
 * 创建兼容API密钥请求DTO
 * <p>
 * 用于创建一个兼容密钥对应多个普通API密钥
 * </p>
 */
@Data
@Schema(description = "创建兼容API密钥请求")
public class CreateCompatibleKeyRequest {

    /**
     * 兼容密钥名称
     */
    @NotBlank(message = "兼容密钥名称不能为空")
    @Size(max = 100, message = "兼容密钥名称长度不能超过100字符")
    @Schema(description = "兼容密钥名称", example = "主要兼容密钥", required = true)
    private String keyName;

    /**
     * 描述信息
     */
    @Size(max = 500, message = "描述信息长度不能超过500字符")
    @Schema(description = "描述信息", example = "用于生产环境的兼容密钥")
    private String description;

    /**
     * 关联的普通API密钥ID列表
     */
    @NotEmpty(message = "必须选择至少一个API密钥")
    @Schema(description = "关联的普通API密钥ID列表", required = true)
    private List<ApiKeyMapping> apiKeyMappings;

    /**
     * 过期时间（可选）
     */
    @Schema(description = "过期时间")
    private Instant expiresAt;

    /**
     * API密钥映射配置
     */
    @Data
    @Schema(description = "API密钥映射配置")
    public static class ApiKeyMapping {
        
        /**
         * 普通API密钥ID
         */
        @Schema(description = "普通API密钥ID", example = "1", required = true)
        private Long userApiKeyId;

        /**
         * 权重（用于负载均衡）
         */
        @Schema(description = "权重（数字越大权重越高）", example = "10")
        private Integer weight = 10;
    }
}
