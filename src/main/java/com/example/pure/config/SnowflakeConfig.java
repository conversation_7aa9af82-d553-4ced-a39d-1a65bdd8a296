package com.example.pure.config;

import com.example.pure.util.SnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 雪花算法ID生成器配置
 * <p>
 * 配置雪花算法的机器ID和数据中心ID
 * </p>
 */
@Slf4j
@Configuration
public class SnowflakeConfig {

    @Value("${app.snowflake.machine-id:1}")
    private long machineId;

    @Value("${app.snowflake.datacenter-id:1}")
    private long datacenterId;

    @Bean
    public SnowflakeIdGenerator snowflakeIdGenerator() {
        log.info("初始化雪花算法ID生成器 - 机器ID: {}, 数据中心ID: {}", machineId, datacenterId);
        return new SnowflakeIdGenerator(machineId, datacenterId);
    }
}
