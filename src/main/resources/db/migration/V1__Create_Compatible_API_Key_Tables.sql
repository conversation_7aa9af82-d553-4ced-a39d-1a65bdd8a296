-- 创建兼容API密钥表
CREATE TABLE IF NOT EXISTS compatible_api_keys (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    snowflake_id BIGINT NOT NULL UNIQUE COMMENT '雪花算法生成的唯一ID',
    key_name VARCHAR(100) NOT NULL COMMENT '兼容密钥名称',
    compatible_key VARCHAR(200) NOT NULL UNIQUE COMMENT '兼容格式的API密钥',
    security_hash VARCHAR(500) NOT NULL COMMENT '安全哈希值',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    description TEXT COMMENT '描述信息',
    usage_count BIGINT NOT NULL DEFAULT 0 COMMENT '使用次数统计',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_snowflake_id (snowflake_id),
    INDEX idx_compatible_key (compatible_key),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兼容API密钥表';

-- 创建兼容密钥映射关系表
CREATE TABLE IF NOT EXISTS compatible_key_mappings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    compatible_key_id BIGINT NOT NULL COMMENT '兼容API密钥ID',
    user_api_key_id BIGINT NOT NULL COMMENT '普通API密钥ID',
    weight INT NOT NULL DEFAULT 10 COMMENT '权重（数字越大权重越高）',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_compatible_key_id (compatible_key_id),
    INDEX idx_user_api_key_id (user_api_key_id),
    INDEX idx_is_active (is_active),
    INDEX idx_weight (weight),
    UNIQUE KEY uk_compatible_user_key (compatible_key_id, user_api_key_id),
    
    FOREIGN KEY (compatible_key_id) REFERENCES compatible_api_keys(id) ON DELETE CASCADE,
    FOREIGN KEY (user_api_key_id) REFERENCES user_api_keys(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兼容密钥映射关系表';

-- 添加注释说明
ALTER TABLE compatible_api_keys COMMENT = '兼容API密钥表 - 存储兼容格式的API密钥信息，一个兼容密钥可以对应多个普通API密钥';
ALTER TABLE compatible_key_mappings COMMENT = '兼容密钥映射关系表 - 存储兼容API密钥与普通API密钥的映射关系，支持权重配置和负载均衡';
